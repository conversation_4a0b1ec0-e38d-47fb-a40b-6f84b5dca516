<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('services', function (Blueprint $table) {
            // Drop the existing foreign key constraint
            $table->dropForeign(['provider_id']);
            
            // Make provider_id nullable
            $table->foreignId('provider_id')->nullable()->change();
            
            // Add the foreign key constraint back with nullable support
            $table->foreign('provider_id')->references('id')->on('providers')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('services', function (Blueprint $table) {
            // Drop the nullable foreign key
            $table->dropForeign(['provider_id']);
            
            // Make provider_id non-nullable again
            $table->foreignId('provider_id')->nullable(false)->change();
            
            // Add back the original constraint
            $table->foreign('provider_id')->references('id')->on('providers')->onDelete('cascade');
        });
    }
};
