# WordPress to Laravel EHR Migration System
## HIPAA/GDPR Compliant | Email-Based Tracking | Real-Time Progress

## Quick Start

### 1. Setup Configuration
```bash
# Configure WordPress connection in .env
WP_API_URL=http://your-wordpress-site.com/wp-admin/admin-ajax.php
WP_API_AUTH="your_wordpress_logged_in_cookie"
WP_API_NONCE="your_wordpress_nonce"
```

### 2. Test Base Migration
```bash
php artisan migratewp:test-base --clinic=1
```

### 3. Run Migration (Step-by-Step Approach - RECOMMENDED)
```bash
# Step 1: Categories First (Global, Independent)
php artisan migratewp:categories --dry-run
php artisan migratewp:categories

# Step 2: Clinics Second (Foundation)
php artisan migratewp:clinics --dry-run
php artisan migratewp:clinics

# Step 3: Users Third (Depends on Clinics)
php artisan migratewp:users --clinic=all --dry-run
php artisan migratewp:users --clinic=all

# Step 4: Services Fourth (Depends on Categories and Clinics)
php artisan migratewp:services --clinic=all --dry-run
php artisan migratewp:services --clinic=all

# Step 5: Static Data Fifth (specializations, etc. - categories handled separately)
php artisan migratewp:staticdata --dry-run
php artisan migratewp:staticdata

# Step 6: Appointments Sixth (Depends on Services and Users)
php artisan migratewp:appointments --clinic=all --dry-run
php artisan migratewp:appointments --clinic=all

# Step 7: Consultations Seventh (Depends on Appointments)
php artisan migratewp:consultations --clinic=all --dry-run
php artisan migratewp:consultations --clinic=all

# Step 8: Prescriptions Eighth (Depends on Consultations)
php artisan migratewp:prescriptions --clinic=all --dry-run
php artisan migratewp:prescriptions --clinic=all

# Step 9: Medical History Ninth (Depends on Users)
php artisan migratewp:medicalhistory --clinic=all --dry-run
php artisan migratewp:medicalhistory --clinic=all

# Step 10: Medical Problems Tenth (Depends on Users)
php artisan migratewp:medicalproblems --clinic=all --dry-run
php artisan migratewp:medicalproblems --clinic=all

# Step 11: Patient Documents Eleventh (Depends on Users)
php artisan migratewp:patientdocuments --clinic=all --dry-run
php artisan migratewp:patientdocuments --clinic=all

# Step 12: Bills Last (Depends on Appointments)
php artisan migratewp:bills --clinic=all --dry-run
php artisan migratewp:bills --clinic=all
```

### Alternative: Combined Commands (Legacy)
```bash
# Foundation Data (Clinics, Users, Services combined)
php artisan migratewp:setupbasedata --clinic=all --dry-run

# Clinical Data (Appointments, Consultations, Prescriptions)
php artisan migratewp:clinicaldata --clinic=all --dry-run
```

## Available Commands

### 🎯 Step-by-Step Migration Commands (RECOMMENDED)

**Foundation Data:**
- `migratewp:categories` - Migrate global service categories (49+ categories)
- `migratewp:clinics` - Migrate clinics only
- `migratewp:users` - Migrate users only
- `migratewp:services` - Migrate services only (depends on categories and clinics)
- `migratewp:staticdata` - Migrate static data only (specializations, etc. - excludes categories)

**Clinical Data:**
- `migratewp:appointments` - Migrate appointments only
- `migratewp:consultations` - Migrate consultations/encounters only
- `migratewp:prescriptions` - Migrate prescriptions only
- `migratewp:medicalhistory` - Migrate medical history only
- `migratewp:medicalproblems` - Migrate medical problems/conditions only
- `migratewp:patientdocuments` - Migrate patient documents only

**Financial Data:**
- `migratewp:bills` - Migrate bills/invoices only

### 📦 Combined Migration Commands (Legacy)
- `migratewp:test-base` - Test API connection and data structure
- `migratewp:setupbasedata` - Migrate foundation data (Clinics, Users, Services)
- `migratewp:clinicaldata` - Migrate clinical data (Appointments, Consultations, Prescriptions, Medical History)

## Detailed Command Documentation

### Categories Migration
```bash
# Migrate global service categories
php artisan migratewp:categories --dry-run
php artisan migratewp:categories
```

**Features:**
- ✅ Migrates 49+ global service categories from WordPress
- ✅ Categories are clinic-independent (global scope)
- ✅ Automatic WordPress category ID mapping (`wp_category_id`)
- ✅ Handles both array and object data formats
- ✅ Comprehensive logging and error handling
- ✅ Uses WordPress static data API endpoint

**Options:**
- `--dry-run` - Preview what would be migrated without executing
- `--force` - Skip confirmation prompts

**Dependencies:** None (independent migration)

### Services Migration
```bash
# Migrate services for specific clinic
php artisan migratewp:services --clinic=1 --dry-run
php artisan migratewp:services --clinic=1

# Migrate services for all clinics
php artisan migratewp:services --clinic=all --dry-run
php artisan migratewp:services --clinic=all
```

**Features:**
- ✅ Automatic category mapping to migrated categories
- ✅ WordPress service ID tracking (`wp_service_id`)
- ✅ Price, duration, and type field mapping
- ✅ Clinic-specific service migration
- ✅ Provider mapping (when providers are migrated)

**Dependencies:** Categories and Clinics must be migrated first

## Common Options
- `--clinic=ID` - Specific clinic ID, comma-separated IDs, or "all"
- `--dry-run` - Preview without executing (RECOMMENDED FIRST)
- `--force` - Skip confirmation prompts
- `--batch-size=500` - Records per batch

## 📁 Dedicated Logging Structure

Each migration command creates dedicated log files:

```
storage/logs/migration/
├── categories/
│   ├── categories_2025_01_21.log       # Success logs
│   └── categories_errors_2025_01_21.log # Error logs
├── clinics/
│   ├── clinics_2025_01_21.log          # Success logs
│   └── clinics_errors_2025_01_21.log   # Error logs
├── users/
│   ├── users_2025_01_21.log
│   └── users_errors_2025_01_21.log
├── services/
│   ├── services_2025_01_21.log
│   └── services_errors_2025_01_21.log
├── static_data/
│   ├── static_data_2025_01_21.log
│   └── static_data_errors_2025_01_21.log
├── appointments/
│   ├── appointments_2025_01_21.log
│   └── appointments_errors_2025_01_21.log
├── consultations/
│   ├── consultations_2025_01_21.log
│   └── consultations_errors_2025_01_21.log
├── prescriptions/
│   ├── prescriptions_2025_01_21.log
│   └── prescriptions_errors_2025_01_21.log
├── medical_history/
│   ├── medical_history_2025_01_21.log
│   └── medical_history_errors_2025_01_21.log
├── medical_problems/
│   ├── medical_problems_2025_01_21.log
│   └── medical_problems_errors_2025_01_21.log
├── patient_documents/
│   ├── patient_documents_2025_01_21.log
│   └── patient_documents_errors_2025_01_21.log
└── bills/
    ├── bills_2025_01_21.log
    └── bills_errors_2025_01_21.log
```

### Benefits of Step-by-Step Approach:
- ✅ **Complete Control** - Run one step at a time
- ✅ **Clear Visibility** - See exactly what each step does
- ✅ **Easy Troubleshooting** - Isolate problems to specific data types
- ✅ **Flexible Execution** - Test single clinics or all at once
- ✅ **Detailed Logging** - Separate logs for each data type
- ✅ **Role Detection** - Shows proper WordPress → Laravel role mapping

## Key Features

### 🔒 HIPAA/GDPR Compliance
- **Comprehensive audit trails** - Every data transformation logged
- **CSV export reports** - For compliance verification
- **Sensitive field detection** - Automatic identification of PHI/PII
- **Session tracking** - Unique session IDs for each migration

### 📧 Email-Based Tracking
- **Primary identifier** - Uses email addresses for data mapping
- **Duplicate detection** - Identifies duplicate email addresses
- **Validation** - Ensures email format compliance
- **Cross-reference** - WordPress ID ↔ Laravel ID ↔ Email mapping

### 📊 Real-Time Progress
- **Live progress bars** - See migration progress in real-time
- **Count tracking** - Processed/Created/Updated/Errors
- **Preview tables** - See data before migration (dry-run)
- **Success rates** - Percentage success for each module

## Getting WordPress Authentication

See `docs/GET_WORDPRESS_AUTH.md` for detailed instructions on obtaining the required WordPress cookie and nonce values.

## WordPress Setup

See `docs/WORDPRESS_SETUP_GUIDE.md` for WordPress plugin configuration.

## Production Execution

### Pre-Migration Checklist
- [ ] Laravel application deployed and tested
- [ ] Fresh WordPress authentication credentials obtained
- [ ] Backup storage space available (2x current DB size)
- [ ] Maintenance window scheduled (2-4 hours)
- [ ] Staging migration tested successfully

### WordPress Authentication Setup
```bash
# Get fresh authentication credentials (see docs/GET_WORDPRESS_AUTH.md)
WP_API_URL=https://your-production-wp.com/wp-admin/admin-ajax.php
WP_API_AUTH="fresh_wordpress_logged_in_cookie"
WP_API_NONCE="current_nonce_value"
```

### Pre-Migration Steps
1. **Enable Maintenance Mode**
   ```bash
   php artisan down --message="System migration in progress"
   ```

2. **Create Full Backup**
   ```bash
   php artisan migratewp:backup --type=full --compress
   ```

3. **Test API Connectivity**
   ```bash
   php artisan migratewp:test-api
   ```

### Production Migration Process (Simplified)
```bash
# 1. Final backup
php artisan migratewp:backup --type=full

# 2. Test base migration
php artisan migratewp:test-base --clinic=1

# 3. Migrate in recommended order (DRY RUN FIRST)
# Step 1: Foundation Data (MUST RUN FIRST)
php artisan migratewp:setupbasedata --clinic=all --dry-run
php artisan migratewp:setupbasedata --clinic=all --force

# Step 2: Clinical Data (Appointments, Consultations, Prescriptions)
php artisan migratewp:clinicaldata --clinic=all --dry-run
php artisan migratewp:clinicaldata --clinic=all --force

# 4. Verify audit reports
# Check: storage/app/migration_audits/
```

### Post-Migration Steps
```bash
# 1. Disable maintenance mode
php artisan up

# 2. Clear all caches
php artisan config:clear && php artisan cache:clear

# 3. Verify critical functionality
# - Test login system
# - Test appointment booking
# - Test payment processing
# - Verify email notifications

# 4. Monitor logs
tail -f storage/logs/laravel.log
```

### Rollback Plan (If Issues Occur)
```bash
# Stop migration and restore from backup
php artisan migratewp:rollback --backup=backup_file.sql

# Or rollback specific module/clinic
php artisan migratewp:rollback --clinic=1 --module=appointments
```

## Migration Output Examples

### Test Command Output
```bash
php artisan migratewp:test-base --clinic=1
```

```
=== TESTING BASE MIGRATION ===

--- TEST 1: API CONNECTION ---
✅ API Connection: SUCCESS
   Response status: OK
   Data count: 3

--- TEST 2: CLINICS DATA ---
✅ Found 3 clinics
✅ Clinic data structure: VALID
   Sample clinic: Downtown Medical (<EMAIL>)

--- TEST 3: USERS DATA ---
✅ Found 25 users for clinic 1
✅ User data structure: VALID
   Sample user: <EMAIL> (ID: 101)
   Role distribution: {"clinic_admin":2,"doctor":8,"patient":12,"receptionist":3}

--- TEST 4: SERVICES DATA ---
✅ Found 15 services for clinic 1
✅ Service data structure: VALID
   Sample service: General Consultation (ID: 101)
   Price: 150

=== BASE MIGRATION TEST COMPLETED ===
```

### Dry Run Output
```bash
php artisan migratewp:setupbasedata --clinic=1 --dry-run
```

```
=== MIGRATING SETUP BASE DATA ===
Session ID: migration_2025_01_21_14_30_45_abc123
DRY RUN MODE - Previewing migration without making changes

--- STEP 1: MIGRATING CLINICS ---
✓ Would migrate 3 clinics

┌─────────────────────────────────────────────────────────────┐
│ WP ID │ Name                │ Email                │ Status │
├─────────────────────────────────────────────────────────────┤
│ 1     │ Downtown Medical    │ <EMAIL>   │ Active │
│ 2     │ Uptown Clinic      │ <EMAIL>      │ Active │
│ 3     │ Westside Health    │ <EMAIL> │ Active │
└─────────────────────────────────────────────────────────────┘

--- STEP 2: MIGRATING USERS ---
[████████████████████] 100% (25/25) | Current: <EMAIL>
✓ Would migrate 25 users

=== EMAIL TRACKING SUMMARY ===
Unique Emails Found: 25
Duplicate Emails: 0
Invalid Emails: 0
HIPAA Sensitive Fields: 47
```

## Troubleshooting

### API Connection Issues
1. Verify WordPress URL is correct
2. Check authentication cookie is valid
3. Ensure nonce is current
4. Test with: `php artisan migratewp:test-api`

### Migration Errors
1. Check logs in `storage/logs/migration.log`
2. Use `--dry-run` to preview changes
3. Start with small batches using `--batch-size=50`

### Multi-Level Data Issues
1. Verify `level` field mapping in WordPress data
2. Check `clinic_id` associations are correct
3. Test inheritance rules after migration

### Common Production Issues & Solutions

**Authentication Expires During Migration**
```bash
# Get fresh credentials and update .env
# Resume migration from last successful point
```

**Memory/Timeout Issues**
```bash
# Reduce batch size for large datasets
php artisan migratewp:appointments --batch-size=100

# Increase timeout in config/migration.php
'timeout' => 300, // 5 minutes
```

**Data Conflicts**
```bash
# Use dry-run to identify conflicts first
php artisan migratewp:services --clinic=1 --dry-run
```

## Audit Reports Generated

After each migration, comprehensive audit reports are generated:

```
storage/app/migration_audits/
├── migration_audit_[session_id].csv      # Complete audit log
├── migration_mappings_[session_id].csv   # WordPress ID ↔ Laravel ID ↔ Email mappings
├── migration_errors_[session_id].csv     # Any errors encountered
└── migration_report_[session_id].json    # Final summary report
```

### CSV Content Example (Data Mappings)
```csv
Session ID,Timestamp,Module,WordPress ID,Laravel ID,Email,WP Data Hash,Laravel Data Hash,Transformations Applied,WP Data Size,Laravel Data Size,Sensitive Fields Count,Email Valid,Sensitive Fields Details
migration_2025_01_21_14_30_45_abc123,2025-01-21T14:30:45Z,users,101,1,<EMAIL>,abc123def,def456ghi,"[""transform_user_data"",""role_mapping""]",1024,856,3,Yes,"[{""field"":""email"",""type"":""email"",""has_data"":true}]"
```

## Simplified File Structure

```
app/Console/Commands/Migration/
├── BaseMigrationCommand.php          # Simple batch processing with transaction safety
├── TestBaseMigration.php             # Test API connection and data structure
│
├── Step-by-Step Commands (RECOMMENDED):
├── MigrateClinics.php                # Clinics only
├── MigrateUsers.php                  # Users only
├── MigrateServices.php               # Services only
├── MigrateStaticData.php             # Static data only
├── MigrateAppointments.php           # Appointments only
├── MigrateConsultations.php          # Consultations/encounters only
├── MigratePrescriptions.php          # Prescriptions only
├── MigrateMedicalHistory.php         # Medical history only
├── MigrateMedicalProblems.php        # Medical problems/conditions only
├── MigratePatientDocuments.php       # Patient documents only
├── MigrateBills.php                  # Bills/invoices only
│
├── Combined Commands (Legacy):
├── MigrateSetupBaseData.php          # Foundation data (Clinics, Users, Services)
└── MigrateClinicalData.php           # Clinical data (Appointments, Consultations, Prescriptions)

app/Services/Migration/
└── DataTransformer.php               # Data transformation methods

config/migration.php                  # Migration configuration
docs/MIGRATION_README.md              # This documentation
```

This simplified system provides:
- ✅ **11 focused commands** for complete step-by-step control
- ✅ **Email-based tracking** for reliable data mapping
- ✅ **Transaction safety** with batch processing and rollback
- ✅ **Dedicated logging** with separate files for each data type
- ✅ **Skip existing emails** to avoid duplicates
- ✅ **Complete visibility** of what each step does
- ✅ **Easy troubleshooting** with isolated data type processing
- ✅ **Full clinical data coverage** - appointments, consultations, prescriptions, medical history, medical problems, patient documents, bills
