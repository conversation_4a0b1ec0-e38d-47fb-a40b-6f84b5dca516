<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Service extends Model
{
    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The primary key type.
     *
     * @var string
     */
    protected $keyType = 'int';

    protected $fillable = [
        'provider_id',
        'category_id',
        'clinic_id',
        'name',
        'description',
        'duration',
        'price',
        'category',
        'type',
        'availability',
        'active',
        'internal',
        'is_telemedicine',
        'discount_percentage',
        'discount_valid_until',
        'approval_status',
        'approved_by',
        'approved_at',
        'rejection_reason',
        'wp_service_id',
    ];

    protected $casts = [
        'duration' => 'integer',
        'price' => 'decimal:2',
        'availability' => 'array',
        'active' => 'boolean',
        'internal' => 'boolean',
        'is_telemedicine' => 'boolean',
        'discount_percentage' => 'decimal:2',
        'discount_valid_until' => 'datetime',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function provider()
    {
        return $this->belongsTo(Provider::class, 'provider_id', 'id');
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Scope to filter internal services
     */
    public function scopeInternal($query)
    {
        return $query->where('internal', true);
    }

    /**
     * Scope to filter external (non-internal) services
     */
    public function scopeExternal($query)
    {
        return $query->where('internal', false);
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class, 'service_id', 'id');
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    public function scopePending($query)
    {
        return $query->where('approval_status', 'pending');
    }

    public function scopeRejected($query)
    {
        return $query->where('approval_status', 'rejected');
    }
}
