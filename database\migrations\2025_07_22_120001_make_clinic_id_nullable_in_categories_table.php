<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            // Drop the existing foreign key constraint
            $table->dropForeign(['clinic_id']);
            
            // Drop the existing unique constraint
            $table->dropUnique(['clinic_id', 'name']);
            
            // Make clinic_id nullable
            $table->foreignId('clinic_id')->nullable()->change();
            
            // Add the foreign key constraint back with nullable support
            $table->foreign('clinic_id')->references('id')->on('clinics')->onDelete('set null');
            
            // Add new unique constraint that handles null clinic_id (global categories)
            // For global categories (clinic_id = null), name should be unique
            // For clinic-specific categories, name should be unique within clinic
            $table->unique(['name'], 'categories_global_name_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            // Drop the nullable foreign key
            $table->dropForeign(['clinic_id']);
            
            // Drop the global unique constraint
            $table->dropUnique('categories_global_name_unique');
            
            // Make clinic_id non-nullable again
            $table->foreignId('clinic_id')->nullable(false)->change();
            
            // Add back the original constraints
            $table->foreign('clinic_id')->references('id')->on('clinics')->onDelete('cascade');
            $table->unique(['clinic_id', 'name']);
        });
    }
};
