<?php

namespace App\Console\Commands\Migration;

use App\Models\Specialization;
use App\Models\Specialty;
use App\Services\Migration\DataTransformer;
use Exception;
use Illuminate\Support\Facades\Log;

/**
 * Migrate Static Data Command
 * 
 * Simple focused migration for static data only (specializations, etc.)
 */
class MigrateStaticData extends BaseMigrationCommand
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'migratewp:staticdata
                            {--dry-run : Preview what would be migrated without executing}
                            {--force : Skip confirmation prompts}';

    /**
     * The console command description.
     */
    protected $description = 'Migrate static data (specializations, etc.) from WordPress to Laravel';

    /**
     * Data transformer instance
     */
    protected $transformer;

    /**
     * Log channels
     */
    protected $logChannel;
    protected $errorLogChannel;

    public function __construct()
    {
        parent::__construct();
        $this->transformer = new DataTransformer();
        $this->setupLogging();
    }

    /**
     * Setup dedicated logging for static data migration
     */
    protected function setupLogging()
    {
        $date = now()->format('Y_m_d');
        $logPath = storage_path("logs/migration/static_data");
        
        // Create directory if it doesn't exist
        if (!file_exists($logPath)) {
            mkdir($logPath, 0755, true);
        }

        // Configure log channels
        config([
            'logging.channels.static_data_migration' => [
                'driver' => 'single',
                'path' => $logPath . "/static_data_{$date}.log",
                'level' => 'info',
            ],
            'logging.channels.static_data_errors' => [
                'driver' => 'single',
                'path' => $logPath . "/static_data_errors_{$date}.log",
                'level' => 'error',
            ]
        ]);

        $this->logChannel = 'static_data_migration';
        $this->errorLogChannel = 'static_data_errors';
    }

    /**
     * Execute the command
     */
    protected function executeCommand()
    {
        $this->info("=== MIGRATING STATIC DATA ===");
        $this->logInfo("Starting static data migration");
        
        if ($this->isDryRun()) {
            $this->warn("DRY RUN MODE - Previewing migration without making changes");
            $this->logInfo("Running in DRY RUN mode");
        }

        try {
            // 1. Make API call - get static data
            $this->info("Fetching static data from WordPress...");
            $this->logInfo("Making API request to get static data");
            
            $response = $this->makeApiRequest('laravel_get_static_data');
            $wpStaticData = $response['data'] ?? [];

            if (empty($wpStaticData)) {
                $this->info("No static data found to migrate");
                $this->logInfo("No static data found in WordPress");
                return 0;
            }

            $this->info("Found static data to migrate");
            $this->logInfo("Found static data to migrate", $wpStaticData);

            $processed = 0;
            $skipped = 0;
            $errors = 0;

            if ($this->isDryRun()) {
                $this->info("Would migrate static data: specializations, etc. (categories handled by dedicated command)");
                $this->logInfo("DRY RUN: Would migrate static data");
                
                // Show what types of static data are available (excluding service_categories)
                if (is_array($wpStaticData)) {
                    foreach ($wpStaticData as $type => $data) {
                        if (is_array($data) && $type !== 'service_categories') {
                            $this->info("  - {$type}: " . count($data) . " items");
                            $this->logInfo("DRY RUN: Would migrate {$type} with " . count($data) . " items");
                        }
                    }
                }

                // Process specializations in dry run mode
                $specializations = $wpStaticData['specializations'] ?? [];
                if (!empty($specializations)) {
                    $this->info("\nDry run processing specializations:");
                    $this->processSpecializations($specializations);
                    $processed++;
                }

                // Process other static data as JSON in dry run mode
                $this->info("\nDry run processing other static data as JSON:");
                $this->processOtherStaticDataAsJson($wpStaticData);
                $processed++;
            } else {
                // Process static data (implementation depends on your static data structure)
                // This is a placeholder - implement based on your specific static data needs
                
                $this->info("Processing static data...");
                $this->logInfo("Processing static data", $wpStaticData);

                // Process different types of static data
                // Note: Categories are handled by dedicated migratewp:categories command

                // Extract specializations from static data (they're in the 'specializations' key)
                $specializations = $wpStaticData['specializations'] ?? [];

                if (!empty($specializations)) {
                    $this->info("Found " . count($specializations) . " specializations to process");
                    $this->processSpecializations($specializations);
                    $processed++;
                } else {
                    $this->info("No specializations found in static data");
                    $this->logInfo("No specializations found in static data");
                }

                // Process other static data types as JSON files
                $this->processOtherStaticDataAsJson($wpStaticData);
                $processed++;

                $this->info("✓ Static data migration completed");
                $this->logInfo("Static data migration completed successfully");
            }

            // 3. Generate summary
            $this->generateSummary($processed, $skipped, $errors);

            return 0;

        } catch (Exception $e) {
            $this->error("Static data migration failed: " . $e->getMessage());
            $this->logError("Static data migration failed: " . $e->getMessage());
            
            // Don't throw exception for static data - it's not critical
            $this->info("Static data migration skipped due to error. This is not critical for the main migration.");
            return 0;
        }
    }

    /**
     * Note: Categories are now handled by dedicated migratewp:categories command
     * This method is removed to avoid conflicts
     */

    /**
     * Process specialties from WordPress static data
     */
    protected function processSpecialties($specialties)
    {
        $this->info("Processing " . count($specialties) . " specialties...");
        $this->logInfo("Processing " . count($specialties) . " specialties", $specialties);

        $processed = 0;
        $skipped = 0;
        $errors = 0;

        foreach ($specialties as $wpSpecialty) {
            try {
                // Handle both array and object formats
                $data = is_array($wpSpecialty) ? $wpSpecialty : (array) $wpSpecialty;
                $label = $data['label'] ?? 'Unknown Specialty';
                $wpId = $data['id'] ?? 0;

                if ($this->isDryRun()) {
                    $this->info("Would migrate specialty: {$label} (WP ID: {$wpId})");
                    $this->logInfo("DRY RUN: Would migrate specialty ID {$wpId}: {$label}");
                    $processed++;
                    continue;
                }

                // Transform WordPress specialty data to Laravel format
                $laravelData = $this->transformer->transformSpecialty($wpSpecialty);

                // Create or update specialty using wp_specialty_id for uniqueness
                $specialty = Specialty::updateOrCreate(
                    ['wp_specialty_id' => $wpId],
                    $laravelData
                );

                $this->info("✓ Migrated specialty: {$specialty->label} (Laravel ID: {$specialty->id})");
                $this->logInfo("Successfully migrated specialty ID {$wpId} → Laravel ID {$specialty->id}: {$specialty->label}");
                $processed++;

            } catch (Exception $e) {
                $wpId = is_array($wpSpecialty) ? ($wpSpecialty['id'] ?? 'unknown') : ($wpSpecialty->id ?? 'unknown');
                $this->error("✗ Failed to migrate specialty ID {$wpId}: " . $e->getMessage());
                $this->logError("Failed to migrate specialty ID {$wpId}: " . $e->getMessage(), is_array($wpSpecialty) ? $wpSpecialty : (array) $wpSpecialty);
                $errors++;
            }
        }

        $this->info("Specialties processing completed: {$processed} processed, {$skipped} skipped, {$errors} errors");
        $this->logInfo("Specialties processing completed: {$processed} processed, {$skipped} skipped, {$errors} errors");
    }

    /**
     * Process specializations from WordPress static data
     */
    protected function processSpecializations($specializations)
    {
        $this->info("Processing " . count($specializations) . " specializations...");
        $this->logInfo("Processing " . count($specializations) . " specializations", $specializations);

        $processed = 0;
        $skipped = 0;
        $errors = 0;

        foreach ($specializations as $wpSpecialization) {
            try {
                // Handle both array and object formats
                $data = is_array($wpSpecialization) ? $wpSpecialization : (array) $wpSpecialization;
                $label = $data['name'] ?? 'Unknown Specialization'; // WordPress uses 'name' field
                $wpId = $data['id'] ?? 0;

                if ($this->isDryRun()) {
                    $this->info("Would migrate specialization: {$label} (WP ID: {$wpId})");
                    $this->logInfo("DRY RUN: Would migrate specialization ID {$wpId}: {$label}");
                    $processed++;
                    continue;
                }

                // Transform WordPress specialization data to Laravel format
                $laravelData = $this->transformer->transformSpecialization($wpSpecialization);

                // Create or update specialization using wp_specialization_id for uniqueness
                $specialization = Specialization::updateOrCreate(
                    ['wp_specialization_id' => $wpId],
                    $laravelData
                );

                $this->info("✓ Migrated specialization: {$specialization->label} (Laravel ID: {$specialization->id})");
                $this->logInfo("Successfully migrated specialization ID {$wpId} → Laravel ID {$specialization->id}: {$specialization->label}");
                $processed++;

            } catch (Exception $e) {
                $wpId = is_array($wpSpecialization) ? ($wpSpecialization['id'] ?? 'unknown') : ($wpSpecialization->id ?? 'unknown');
                $this->error("✗ Failed to migrate specialization ID {$wpId}: " . $e->getMessage());
                $this->logError("Failed to migrate specialization ID {$wpId}: " . $e->getMessage(), is_array($wpSpecialization) ? $wpSpecialization : (array) $wpSpecialization);
                $errors++;
            }
        }

        $this->info("Specializations processing completed: {$processed} processed, {$skipped} skipped, {$errors} errors");
        $this->logInfo("Specializations processing completed: {$processed} processed, {$skipped} skipped, {$errors} errors");
    }

    /**
     * Generate migration summary
     */
    protected function generateSummary($processed, $skipped, $errors)
    {
        $this->info("\n" . str_repeat("=", 60));
        $this->info("📊 STATIC DATA MIGRATION SUMMARY");
        $this->info(str_repeat("=", 60));
        $this->info("✅ Processed: {$processed}");
        $this->info("⏭️  Skipped: {$skipped}");
        $this->info("❌ Errors: {$errors}");
        
        if ($errors > 0) {
            $this->error("\n⚠️  Some static data failed to migrate. Check error logs for details.");
        } else {
            $this->info("\n🎉 Static data migrated successfully!");
        }
        
        $this->info("📁 Logs saved to: storage/logs/migration/static_data/");
        $this->info(str_repeat("=", 60));

        // Log summary
        $this->logInfo("Migration completed - Processed: {$processed}, Skipped: {$skipped}, Errors: {$errors}");
    }

    /**
     * Process other static data types as JSON files
     */
    protected function processOtherStaticDataAsJson($wpStaticData)
    {
        $this->info("Processing other static data as JSON files...");

        // Define which data types to save as JSON (excluding categories and specializations)
        $jsonDataTypes = ['appointment_statuses', 'payment_statuses', 'medical_history_types'];

        $processed = 0;
        $storageDir = storage_path('app/static_data');

        // Create directory if it doesn't exist
        if (!is_dir($storageDir)) {
            mkdir($storageDir, 0755, true);
        }

        foreach ($jsonDataTypes as $dataType) {
            if (isset($wpStaticData[$dataType]) && is_array($wpStaticData[$dataType])) {
                $data = $wpStaticData[$dataType];
                $filename = $storageDir . '/' . $dataType . '.json';

                if ($this->isDryRun()) {
                    $this->info("Would save {$dataType}: " . count($data) . " items to {$filename}");
                    $this->logInfo("DRY RUN: Would save {$dataType} to JSON file", $data);
                } else {
                    file_put_contents($filename, json_encode($data, JSON_PRETTY_PRINT));
                    $this->info("✓ Saved {$dataType}: " . count($data) . " items to {$filename}");
                    $this->logInfo("Saved {$dataType} to JSON file: {$filename}", $data);
                }
                $processed++;
            }
        }

        $this->info("JSON static data processing completed: {$processed} files processed");
        $this->logInfo("JSON static data processing completed: {$processed} files processed");
    }

    /**
     * Log info message
     */
    protected function logInfo($message, $context = [])
    {
        Log::channel($this->logChannel)->info($message, $context);
    }

    /**
     * Log error message
     */
    protected function logError($message, $context = [])
    {
        Log::channel($this->errorLogChannel)->error($message, $context);
    }
}
