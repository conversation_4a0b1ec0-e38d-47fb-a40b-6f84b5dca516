<?php

namespace App\Console\Commands\Migration;

use App\Models\Category;
use App\Services\Migration\DataTransformer;
use Exception;
use Illuminate\Support\Facades\Log;

/**
 * Migrate Categories Command
 * 
 * Simple focused migration for categories only
 */
class MigrateCategories extends BaseMigrationCommand
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'migratewp:categories
                            {--clinic= : Specific clinic ID, comma-separated IDs, or "all" for all clinics}
                            {--dry-run : Preview what would be migrated without executing}
                            {--force : Skip confirmation prompts}';

    /**
     * The console command description.
     */
    protected $description = 'Migrate categories from WordPress to Laravel';

    /**
     * Data transformer instance
     */
    protected $transformer;

    /**
     * Log channels
     */
    protected $logChannel;
    protected $errorLogChannel;

    public function __construct()
    {
        parent::__construct();
        $this->transformer = new DataTransformer();
        $this->setupLogging();
    }

    /**
     * Setup dedicated logging for categories migration
     */
    protected function setupLogging()
    {
        $date = now()->format('Y_m_d');
        $logPath = storage_path("logs/migration/categories");
        
        // Create directory if it doesn't exist
        if (!file_exists($logPath)) {
            mkdir($logPath, 0755, true);
        }

        // Configure log channels
        config([
            'logging.channels.categories_migration' => [
                'driver' => 'single',
                'path' => $logPath . "/categories_{$date}.log",
                'level' => 'info',
            ],
            'logging.channels.categories_errors' => [
                'driver' => 'single',
                'path' => $logPath . "/categories_errors_{$date}.log",
                'level' => 'error',
            ]
        ]);

        $this->logChannel = 'categories_migration';
        $this->errorLogChannel = 'categories_errors';
    }

    /**
     * Execute the command
     */
    protected function executeCommand()
    {
        $this->info("=== MIGRATING CATEGORIES ===");
        $this->logInfo("Starting categories migration");
        
        if ($this->isDryRun()) {
            $this->warn("DRY RUN MODE - Previewing migration without making changes");
            $this->logInfo("Running in DRY RUN mode");
        }

        // Get global categories (independent of clinics)
        try {
            $this->info("Getting global categories...");
            $this->logInfo("Getting global categories");

            // Get categories from static data
            $response = $this->makeApiRequest('laravel_get_static_data');
            $staticData = $response['data'] ?? [];
            $categories = $staticData['service_categories'] ?? [];

            if (empty($categories)) {
                $this->info("No categories found in global static data");
                $this->logInfo("No categories found in global static data");
                return 0;
            }

            $this->info("Found " . count($categories) . " global categories");
            $this->logInfo("Found " . count($categories) . " global categories");

            $processed = 0;
            $skipped = 0;
            $errors = 0;

            // Process each category
            foreach ($categories as $wpCategory) {
                try {
                    // Handle both array and object formats
                    $data = is_array($wpCategory) ? $wpCategory : (array) $wpCategory;
                    $categoryName = $data['name'] ?? 'Unknown Category';
                    $categoryId = $data['id'] ?? 0;

                    if ($this->isDryRun()) {
                        $this->info("Would migrate global category: {$categoryName} (ID: {$categoryId})");
                        $this->logInfo("DRY RUN: Would migrate category ID {$categoryId}: {$categoryName}");
                        $processed++;
                        continue;
                    }

                    // Transform WordPress category data to Laravel format
                    $laravelData = $this->transformer->transformCategory($wpCategory);
                    // Categories are global, so clinic_id should be null
                    $laravelData['clinic_id'] = null;

                    // Create or update category using wp_category_id for uniqueness
                    $category = Category::updateOrCreate(
                        ['wp_category_id' => $categoryId],
                        $laravelData
                    );

                    $this->info("✓ Migrated global category: {$category->name} (Laravel ID: {$category->id})");
                    $this->logInfo("Successfully migrated category ID {$categoryId} → Laravel ID {$category->id}: {$category->name}");
                    $processed++;

                } catch (Exception $e) {
                    $categoryId = is_array($wpCategory) ? ($wpCategory['id'] ?? 'unknown') : ($wpCategory->id ?? 'unknown');
                    $this->error("✗ Failed to migrate category ID {$categoryId}: " . $e->getMessage());
                    $this->logError("Failed to migrate category ID {$categoryId}: " . $e->getMessage(), is_array($wpCategory) ? $wpCategory : (array) $wpCategory);
                    $errors++;
                }
            }

            // Generate summary
            $this->generateSummary($processed, $skipped, $errors, []);

        } catch (Exception $e) {
            $this->error("❌ Failed to get categories: " . $e->getMessage());
            $this->logError("Failed to get categories: " . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Generate migration summary
     */
    protected function generateSummary($processed, $skipped, $errors, $failedClinics)
    {
        $this->info("\n" . str_repeat("=", 60));
        $this->info("📊 CATEGORIES MIGRATION SUMMARY");
        $this->info(str_repeat("=", 60));
        $this->info("✅ Processed: {$processed}");
        $this->info("⏭️  Skipped: {$skipped}");
        $this->info("❌ Errors: {$errors}");

        if (!empty($failedClinics)) {
            $this->error("🚨 Failed Clinics: " . implode(', ', $failedClinics));
        }

        if ($errors > 0 || !empty($failedClinics)) {
            $this->error("\n⚠️  Some categories failed to migrate. Check error logs for details.");
        } else {
            $this->info("\n🎉 All categories migrated successfully!");
        }

        $this->info("📁 Logs saved to: storage/logs/migration/categories/");
        $this->info(str_repeat("=", 60));

        // Log summary
        $this->logInfo("Migration completed - Processed: {$processed}, Skipped: {$skipped}, Errors: {$errors}");
        if (!empty($failedClinics)) {
            $this->logError("Failed clinics: " . implode(', ', $failedClinics));
        }
    }

    /**
     * Log info message
     */
    protected function logInfo($message, $context = [])
    {
        Log::channel($this->logChannel)->info($message, $context);
    }

    /**
     * Log error message
     */
    protected function logError($message, $context = [])
    {
        Log::channel($this->errorLogChannel)->error($message, $context);
    }
}
