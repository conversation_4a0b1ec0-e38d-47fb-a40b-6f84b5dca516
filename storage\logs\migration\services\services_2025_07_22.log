[2025-07-22 11:31:00] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 11:31:00] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 11:31:06] local.INFO: WordPress API connection validated  
[2025-07-22 11:31:06] local.INFO: Database connection validated  
[2025-07-22 11:31:06] local.INFO: Starting services migration  
[2025-07-22 11:31:06] local.INFO: Running in DRY RUN mode  
[2025-07-22 11:31:06] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 11:31:11] local.INFO: Starting services migration for clinic 40  
[2025-07-22 11:31:11] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:31:15] local.INFO: Found 3 services for clinic 40  
[2025-07-22 11:31:15] local.INFO: DRY RUN: Would migrate service ID 320: Medication (Price: 200)  
[2025-07-22 11:31:15] local.INFO: DRY RUN: Would migrate service ID 321: Blood Tests (Price: 150)  
[2025-07-22 11:31:15] local.INFO: DRY RUN: Would migrate service ID 322: Virtual consultation (Price: 120)  
[2025-07-22 11:31:15] local.INFO: Clinic 40 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 11:31:15] local.INFO: Starting services migration for clinic 27  
[2025-07-22 11:31:15] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:31:20] local.INFO: No services found for clinic 27  
[2025-07-22 11:31:20] local.INFO: Starting services migration for clinic 26  
[2025-07-22 11:31:20] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:31:25] local.INFO: No services found for clinic 26  
[2025-07-22 11:31:25] local.INFO: Starting services migration for clinic 22  
[2025-07-22 11:31:25] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:31:30] local.INFO: No services found for clinic 22  
[2025-07-22 11:31:30] local.INFO: Starting services migration for clinic 16  
[2025-07-22 11:31:30] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:31:36] local.INFO: Found 3 services for clinic 16  
[2025-07-22 11:31:36] local.INFO: DRY RUN: Would migrate service ID 195: Blood Pressure Check (Price: 20)  
[2025-07-22 11:31:36] local.INFO: DRY RUN: Would migrate service ID 230: PCP Appointment (Price: 200)  
[2025-07-22 11:31:36] local.INFO: DRY RUN: Would migrate service ID 231: Cyst Removal (Price: 150)  
[2025-07-22 11:31:36] local.INFO: Clinic 16 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 11:31:36] local.INFO: Starting services migration for clinic 15  
[2025-07-22 11:31:36] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:31:41] local.INFO: Found 2 services for clinic 15  
[2025-07-22 11:31:41] local.INFO: DRY RUN: Would migrate service ID 110: Urgent Care (In-Person) (Price: 100)  
[2025-07-22 11:31:41] local.INFO: DRY RUN: Would migrate service ID 111: Urgent Care (Virtual) (Price: 75)  
[2025-07-22 11:31:41] local.INFO: Clinic 15 completed: 2 processed, 0 skipped, 0 errors  
[2025-07-22 11:31:41] local.INFO: Starting services migration for clinic 14  
[2025-07-22 11:31:41] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:31:46] local.INFO: Found 136 services for clinic 14  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 68: Dressing Clinic (Price: 75)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 69: STI Check - Women only (Price: 120)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 77: Period Clinic (Menstrual Disorders) (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 78: Pelvic Pain (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 79: Menopause (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 80: Post-reproductive Women's Health (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 81: General Womens Health (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 82: Vulval and Vaginal Health (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 84: Recurrent Miscarriages (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 90: Coil Fitting (Price: 500)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 98: Recurrent Miscarriage Testing (Price: 750)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 99: Follicle Tracking Scan (Price: 160)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 103: Vulvoscopy (Price: 675)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 107: Pelvic Ultrasound (Consultant) (Price: 300)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 112: Private GP - In Person (Price: 100)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 119: Private GP Virtual (Price: 75)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 121: Weight Loss (Price: 225)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 122: Weight Loss (Online) (Price: 225)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 124: Urgent Care (Virtual) (Price: 75)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 125: Menopausal Health Check (Price: 375)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 126: Coil Removal (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 127: Pre-Pregnancy Health Check (Price: 400)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 128: Ovarian Cancer Screening (Price: 550)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 129: Recurrent Thrush (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 130: PCOS Consultation (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 131: Endometriosis (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 132: Post Natal Care (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 133: Fibroids Consultation (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 134: Fertility Health Check (Price: 450)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 135: Mini Sexual Health Check (Price: 350)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 136: Comprehensive Sexual Health Check (Price: 500)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 138: General Health Check (Price: 300)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 146: Basic Health Check (Price: 199)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 147: Over 50 Men's Health Check (Price: 295)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 148: Over 50 Women's Health Check (Price: 350)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 149: Heart Health Check (Price: 375)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 150: Comprehensive Health Check (Price: 475)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 154: Cyst Removal (Price: 300)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 158: Smear Test (Price: 180)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 160: Urgent Care Follow-Up (In-Person ) (Price: 75)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 169: Ultrasound Foot and Ankle (Price: 175)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 170: Shoulder and Upper Arm Scan (Price: 150)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 181: Upper Abdominal Scan (Price: 53)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 182: Thyroid Scan (Price: 94)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 189: Abdominal Aorta Scan (Price: 100)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 196: Wound Swab (Price: 70)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 198: Knee Scan (Price: 175)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 203: Follow-Up GP Appointment (Price: 75)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 204: Phlebotomy (Blood Draw) (Price: 25)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 206: Aesthetics Consultation (Redeemable) (Price: 50)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 216: Skin Specialist GP Consultation (Price: 120)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 220: Gynaecology New Consultation (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 221: Gynaecology Follow-up (Price: 175)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 235: Urology New Consultation (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 239: Durolane Injections (Price: 300)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 240: Ostenil Plus Injections (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 241: Platelet-Rich Plasma (PRP) (Price: 400)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 243: Respiratory Follow-up Consultation (Price: 175)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 244: Heart Health Consultation (Price: 250)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 245: Men's Health Consultation (Price: 100)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 247: Drivers Medicals (Price: 75)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 260: Pelvic Scan - Well women (Price: 120)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 262: Sexual Health Screen 3 (7 STD Swabs) (Price: 140)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 263: Sexual Health Screen 2 (Blood tests and Swab) (Price: 120)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 266: Sexual Health Screen 1 (Chlamydia+Gonorrhoea) (Price: 75)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 267: Initial consultation - Skin Procedures (Price: 75)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 269: Flexible Cystoscopy (Price: 725)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 274: Fertility - Monitoring Scan (Price: 165)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 275: Fertility - Baseline Scan (Price: 300)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 278: Breast with Armpit (Both Sides) (Price: 300)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 279: Tetanus Shot (Price: 50)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 280: Sexual Health Screen 3 (7 STD Swabs) (Price: 175)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 281: Prostate Scan (Price: 175)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 282: Testicular Scan (Price: 160)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 286: Breast with Armpit (Price: 175)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 287: Wrist and Hand (Price: 175)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 289: Hip Scan (Price: 175)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 290: Ankle and Foot Scan (Price: 175)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 291: Fertility - Endometrial Thickness (Price: 90)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 299: ECG (Price: 60)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 302: Upper Abdominal and pelvic scan (Price: 200)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 308: Weight Loss Consultation (virtual) (Price: 89)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 317: General Surgery Consultation (Price: 275)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 318: General Surgery Follow Up (Price: 175)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 327: Urology Follow-up (Price: 200)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 337: Steroid Injections (Price: 200)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 339: Fertility Counselling (Price: 120)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 345: Women's Health Specialist Consultation (Price: 120)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 349: Echocardiogram (Heart Scan) (Price: 400)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 353: Neck Scan (Price: 175)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 354: Heart New Appointment (Price: 275)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 355: Heart Follow-up (Price: 200)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 366: Virtual Women's Health Specialist Consultation (Price: 100)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 368: Urgent Care - In-Person (Price: 100)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 373: Blood Tests (Price: 100)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 374: Blood Tests (Price: 100)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 378: Blood Tests (Price: 100)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 379: Urgent Care (In-Person) (Price: 100)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 380: Private GP - In Person (Price: 100)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 381: Urgent Care (Virtual) (Price: 75)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 382: Ear Microsuction (Both ears) (Price: 45)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 383: Cyst Removal (Price: 300)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 384: Soft Tissue Lumps and Bumps Scan (Price: 350)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 386: Doppler Lower Limb (One Side) (Price: 100)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 387: Sexual Health Screen 3 (7 STD Swabs) (Price: 175)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 388: Phlebotomy (Price: 210)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 389: Blood test (Price: 210)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 390: ECG (Price: 60)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 391: Blood test referral (Price: 72)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 392: Medication (Price: 200)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 393: Blood Tests (Price: 150)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 394: WEIGHT LOSS MEDICATION (Price: 0)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 395: MRI Scan (One Part) (Price: 380)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 396: Medication (Price: 275)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 397: Medication (Price: 25)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 398: Medication (Price: 25)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 401: Blood Test (Price: 75)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 402: Blood Test (Price: 75)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 406: Recurrent Thrush Package (Price: 750)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 407: Urgent Care (In-Person) (Price: 100)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 408: Private GP - In Person (Price: 75)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 409: Urgent Care (Virtual) (Price: 400)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 410: Cyst Removal (Price: 300)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 411: Soft Tissue Lumps and Bumps Scan (Price: 350)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 413: Doppler Lower Limb (One Side) (Price: 100)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 414: Sexual Health Screen 3 (7 STD Swabs) (Price: 175)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 415: Phlebotomy (Price: 210)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 416: Blood test (Price: 210)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 417: Blood test referral (Price: 72)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 418: Medication (Price: 200)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 419: Blood Tests (Price: 150)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 420: WEIGHT LOSS MEDICATION (Price: 0)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 421: MRI Scan (One Part) (Price: 380)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 422: Medication (Price: 275)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 423: Medication (Price: 25)  
[2025-07-22 11:31:46] local.INFO: DRY RUN: Would migrate service ID 424: Medication (Price: 25)  
[2025-07-22 11:31:46] local.INFO: Clinic 14 completed: 136 processed, 0 skipped, 0 errors  
[2025-07-22 11:31:46] local.INFO: Starting services migration for clinic 13  
[2025-07-22 11:31:46] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:31:50] local.INFO: Found 1 services for clinic 13  
[2025-07-22 11:31:50] local.INFO: DRY RUN: Would migrate service ID 39: Medical Assessment (Price: 60)  
[2025-07-22 11:31:50] local.INFO: Clinic 13 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 11:31:50] local.INFO: Starting services migration for clinic 12  
[2025-07-22 11:31:50] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:31:55] local.INFO: Found 12 services for clinic 12  
[2025-07-22 11:31:55] local.INFO: DRY RUN: Would migrate service ID 36: Dermatology consultation (Price: 95)  
[2025-07-22 11:31:55] local.INFO: DRY RUN: Would migrate service ID 228: Repeat Prescriptions only (will require proof of prescription) (Price: 30)  
[2025-07-22 11:31:55] local.INFO: DRY RUN: Would migrate service ID 305: Blood tests (Price: 38)  
[2025-07-22 11:31:55] local.INFO: DRY RUN: Would migrate service ID 306: Video/Telephone GP appointment (Standard) (Price: 55)  
[2025-07-22 11:31:55] local.INFO: DRY RUN: Would migrate service ID 307: Video/Telephone GP appointment (long) (Price: 89)  
[2025-07-22 11:31:55] local.INFO: DRY RUN: Would migrate service ID 309: Referral letter (with standard consultation) (Price: 89)  
[2025-07-22 11:31:55] local.INFO: DRY RUN: Would migrate service ID 310: Sick note / Medical certificate (Price: 89)  
[2025-07-22 11:31:55] local.INFO: DRY RUN: Would migrate service ID 311: Women's Health consultation (Price: 94)  
[2025-07-22 11:31:55] local.INFO: DRY RUN: Would migrate service ID 312: Mental health consultation (Price: 96)  
[2025-07-22 11:31:55] local.INFO: DRY RUN: Would migrate service ID 404: Weight loss Injection enquiry - Mounjaro (Price: 0)  
[2025-07-22 11:31:55] local.INFO: DRY RUN: Would migrate service ID 405: Weight loss injections (Price: 0)  
[2025-07-22 11:31:55] local.INFO: DRY RUN: Would migrate service ID 425: Aesthetics Initial consultation (Price: 13)  
[2025-07-22 11:31:55] local.INFO: Clinic 12 completed: 12 processed, 0 skipped, 0 errors  
[2025-07-22 11:31:55] local.INFO: Starting services migration for clinic 11  
[2025-07-22 11:31:55] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:32:00] local.INFO: Found 7 services for clinic 11  
[2025-07-22 11:32:00] local.INFO: DRY RUN: Would migrate service ID 7: ⁠Urgent Telephone or Video consultation on request (Price: 85)  
[2025-07-22 11:32:00] local.INFO: DRY RUN: Would migrate service ID 8: ⁠NMC health declaration Video consultation (Price: 85)  
[2025-07-22 11:32:00] local.INFO: DRY RUN: Would migrate service ID 9: Menopause consultation ( initial appointment) (Price: 85)  
[2025-07-22 11:32:00] local.INFO: DRY RUN: Would migrate service ID 64: Mental health consultation (Initial appointment) (Price: 85)  
[2025-07-22 11:32:00] local.INFO: DRY RUN: Would migrate service ID 350: Prostate Health consultation +PSA (Price: 150)  
[2025-07-22 11:32:00] local.INFO: DRY RUN: Would migrate service ID 351: Virtual consultation (Price: 85)  
[2025-07-22 11:32:00] local.INFO: DRY RUN: Would migrate service ID 352: Telephone or Video consultation (standard) (Price: 55)  
[2025-07-22 11:32:00] local.INFO: Clinic 11 completed: 7 processed, 0 skipped, 0 errors  
[2025-07-22 11:32:00] local.INFO: Starting services migration for clinic 10  
[2025-07-22 11:32:00] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:32:06] local.INFO: Found 2 services for clinic 10  
[2025-07-22 11:32:06] local.INFO: DRY RUN: Would migrate service ID 61: General Consultations (Price: 60)  
[2025-07-22 11:32:06] local.INFO: DRY RUN: Would migrate service ID 62: Weight Loss (Price: 120)  
[2025-07-22 11:32:06] local.INFO: Clinic 10 completed: 2 processed, 0 skipped, 0 errors  
[2025-07-22 11:32:06] local.INFO: Starting services migration for clinic 9  
[2025-07-22 11:32:06] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:32:10] local.INFO: Found 1 services for clinic 9  
[2025-07-22 11:32:10] local.INFO: DRY RUN: Would migrate service ID 30: Initial consultation (Price: 100)  
[2025-07-22 11:32:10] local.INFO: Clinic 9 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 11:32:10] local.INFO: Starting services migration for clinic 8  
[2025-07-22 11:32:10] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:32:13] local.INFO: Found 8 services for clinic 8  
[2025-07-22 11:32:13] local.INFO: DRY RUN: Would migrate service ID 12: Initial GP consultation (Price: 90)  
[2025-07-22 11:32:13] local.INFO: DRY RUN: Would migrate service ID 49: Virtual (Price: 75)  
[2025-07-22 11:32:13] local.INFO: DRY RUN: Would migrate service ID 144: Virtual Follow-up (Price: 65)  
[2025-07-22 11:32:13] local.INFO: DRY RUN: Would migrate service ID 145: Phlebotomy (Price: 50)  
[2025-07-22 11:32:13] local.INFO: DRY RUN: Would migrate service ID 168: Wellman Prostate health check (Price: 90)  
[2025-07-22 11:32:13] local.INFO: DRY RUN: Would migrate service ID 171: GP Follow-up (Price: 75)  
[2025-07-22 11:32:13] local.INFO: DRY RUN: Would migrate service ID 172: Cardiovascular / Diabetes Health check (Price: 350)  
[2025-07-22 11:32:13] local.INFO: DRY RUN: Would migrate service ID 173: Soft Tissue Lumps and Bumps Scan (Price: 350)  
[2025-07-22 11:32:13] local.INFO: Clinic 8 completed: 8 processed, 0 skipped, 0 errors  
[2025-07-22 11:32:13] local.INFO: Starting services migration for clinic 6  
[2025-07-22 11:32:13] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:32:17] local.INFO: Found 1 services for clinic 6  
[2025-07-22 11:32:17] local.INFO: DRY RUN: Would migrate service ID 142: Video Consultation (Price: 500)  
[2025-07-22 11:32:17] local.INFO: Clinic 6 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 11:32:17] local.INFO: Starting services migration for clinic 5  
[2025-07-22 11:32:17] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:32:23] local.INFO: Found 23 services for clinic 5  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 16: Initial with pen - clinic (Price: 250)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 18: Initial with pen (video) (Price: 250)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 21: 1 ear (Price: 50)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 22: Minor illness - video (Price: 65)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 23: follow up with pen - video (Price: 225)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 24: follow up with pen -clinic (Price: 225)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 25: Minor illness -clinic (Price: 65)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 40: both ears (Price: 70)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 41: Dressing -simple (Price: 45)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 42: Dressing- complex (Price: 60)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 43: Suture/Clip removal (Price: 45)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 44: own medication (Price: 25)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 45: B12 (Price: 30)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 46: biotin (Price: 60)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 47: own form (Price: 35)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 249: IV administration only (Price: 80)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 357: Video Follow up - 7.5mg/10mg (Price: 235)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 358: Video Follow up - 12.5mg/15mg (Price: 250)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 359: In-Clinic Follow up - 2.5mg/5mg (Price: 225)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 360: In-Clinic Follow up - 5mg/7.5mg (Price: 235)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 361: In-Clinic Follow up - 12.5mg/15mg (Price: 250)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 400: Video Follow up - 2.5mg/5mg (Price: 175)  
[2025-07-22 11:32:23] local.INFO: DRY RUN: Would migrate service ID 426: Video Follow up - 2.5mg/5mg (Price: 178)  
[2025-07-22 11:32:23] local.INFO: Clinic 5 completed: 23 processed, 0 skipped, 0 errors  
[2025-07-22 11:32:23] local.INFO: Starting services migration for clinic 4  
[2025-07-22 11:32:23] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:32:29] local.INFO: No services found for clinic 4  
[2025-07-22 11:32:29] local.INFO: Starting services migration for clinic 2  
[2025-07-22 11:32:29] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:32:35] local.INFO: No services found for clinic 2  
[2025-07-22 11:32:35] local.INFO: Starting services migration for clinic 1  
[2025-07-22 11:32:35] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 11:32:40] local.INFO: Found 3 services for clinic 1  
[2025-07-22 11:32:40] local.INFO: DRY RUN: Would migrate service ID 17: GP Services (Price: 33)  
[2025-07-22 11:32:40] local.INFO: DRY RUN: Would migrate service ID 60: GP Consultation (Virtual) Test (Price: 10)  
[2025-07-22 11:32:40] local.INFO: DRY RUN: Would migrate service ID 215: Same Day GP (Price: 59)  
[2025-07-22 11:32:40] local.INFO: Clinic 1 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 11:32:40] local.INFO: Migration completed - Processed: 202, Skipped: 0, Errors: 0  
[2025-07-22 11:32:40] local.INFO: Migration command completed successfully  
[2025-07-22 13:15:16] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 13:15:16] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:15:21] local.INFO: WordPress API connection validated  
[2025-07-22 13:15:21] local.INFO: Database connection validated  
[2025-07-22 13:15:21] local.INFO: Starting services migration  
[2025-07-22 13:15:21] local.INFO: Running in DRY RUN mode  
[2025-07-22 13:15:21] local.INFO: Starting services migration for clinic 14  
[2025-07-22 13:15:21] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 13:15:26] local.INFO: No services found for clinic 14  
[2025-07-22 13:15:26] local.INFO: Migration completed - Processed: 0, Skipped: 0, Errors: 0  
[2025-07-22 13:15:26] local.INFO: Migration command completed successfully  
[2025-07-22 13:15:34] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 13:15:34] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:15:37] local.INFO: WordPress API connection validated  
[2025-07-22 13:15:37] local.INFO: Database connection validated  
[2025-07-22 13:15:37] local.INFO: Starting services migration  
[2025-07-22 13:15:37] local.INFO: Running in DRY RUN mode  
[2025-07-22 13:15:37] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:15:42] local.INFO: Migration completed - Processed: 0, Skipped: 0, Errors: 0  
[2025-07-22 13:15:42] local.INFO: Migration command completed successfully  
[2025-07-22 13:20:36] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 13:20:36] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:20:39] local.INFO: WordPress API connection validated  
[2025-07-22 13:20:39] local.INFO: Database connection validated  
[2025-07-22 13:20:39] local.INFO: Starting services migration  
[2025-07-22 13:20:39] local.INFO: Running in DRY RUN mode  
[2025-07-22 13:20:39] local.INFO: Starting services migration for clinic 14  
[2025-07-22 13:20:39] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 13:20:44] local.INFO: No services found for clinic 14  
[2025-07-22 13:20:44] local.INFO: Migration completed - Processed: 0, Skipped: 0, Errors: 0  
[2025-07-22 13:20:44] local.INFO: Migration command completed successfully  
[2025-07-22 13:20:51] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 13:20:51] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:20:56] local.INFO: WordPress API connection validated  
[2025-07-22 13:20:56] local.INFO: Database connection validated  
[2025-07-22 13:20:56] local.INFO: Starting services migration  
[2025-07-22 13:20:56] local.INFO: Running in DRY RUN mode  
[2025-07-22 13:20:56] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:21:03] local.INFO: Migration completed - Processed: 0, Skipped: 0, Errors: 0  
[2025-07-22 13:21:03] local.INFO: Migration command completed successfully  
[2025-07-22 13:27:39] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 13:27:39] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:27:44] local.INFO: WordPress API connection validated  
[2025-07-22 13:27:44] local.INFO: Database connection validated  
[2025-07-22 13:27:44] local.INFO: Starting services migration  
[2025-07-22 13:27:44] local.INFO: Running in DRY RUN mode  
[2025-07-22 13:27:44] local.INFO: Starting services migration for clinic 40  
[2025-07-22 13:27:44] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 13:27:50] local.INFO: Found 3 services for clinic 40  
[2025-07-22 13:27:50] local.INFO: DRY RUN: Would migrate service ID 320: Medication (Price: 200, Category ID: 4)  
[2025-07-22 13:27:50] local.INFO: DRY RUN: Would migrate service ID 321: Blood Tests (Price: 150, Category ID: 39)  
[2025-07-22 13:27:50] local.INFO: DRY RUN: Would migrate service ID 322: Virtual consultation (Price: 120, Category ID: 4)  
[2025-07-22 13:27:50] local.INFO: Clinic 40 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 13:27:50] local.INFO: Migration completed - Processed: 3, Skipped: 0, Errors: 0  
[2025-07-22 13:27:50] local.INFO: Migration command completed successfully  
[2025-07-22 13:27:59] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 13:27:59] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:28:03] local.INFO: WordPress API connection validated  
[2025-07-22 13:28:03] local.INFO: Database connection validated  
[2025-07-22 13:28:03] local.INFO: Starting services migration  
[2025-07-22 13:28:03] local.INFO: Starting services migration for clinic 40  
[2025-07-22 13:28:03] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 13:28:09] local.INFO: Found 3 services for clinic 40  
[2025-07-22 13:28:09] local.INFO: Clinic 40 completed: 0 processed, 0 skipped, 3 errors  
[2025-07-22 13:28:09] local.INFO: Migration completed - Processed: 0, Skipped: 0, Errors: 3  
[2025-07-22 13:28:09] local.INFO: Migration command completed successfully  
[2025-07-22 13:29:25] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 13:29:25] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:29:30] local.INFO: WordPress API connection validated  
[2025-07-22 13:29:30] local.INFO: Database connection validated  
[2025-07-22 13:29:30] local.INFO: Starting services migration  
[2025-07-22 13:29:30] local.INFO: Starting services migration for clinic 40  
[2025-07-22 13:29:30] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 13:29:35] local.INFO: Found 3 services for clinic 40  
[2025-07-22 13:29:35] local.INFO: Clinic 40 completed: 0 processed, 0 skipped, 3 errors  
[2025-07-22 13:29:35] local.INFO: Migration completed - Processed: 0, Skipped: 0, Errors: 3  
[2025-07-22 13:29:35] local.INFO: Migration command completed successfully  
[2025-07-22 13:30:02] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 13:30:02] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:30:07] local.INFO: WordPress API connection validated  
[2025-07-22 13:30:07] local.INFO: Database connection validated  
[2025-07-22 13:30:07] local.INFO: Starting services migration  
[2025-07-22 13:30:07] local.INFO: Starting services migration for clinic 40  
[2025-07-22 13:30:07] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 13:30:11] local.INFO: Found 3 services for clinic 40  
[2025-07-22 13:30:11] local.INFO: Clinic 40 completed: 0 processed, 0 skipped, 3 errors  
[2025-07-22 13:30:11] local.INFO: Migration completed - Processed: 0, Skipped: 0, Errors: 3  
[2025-07-22 13:30:11] local.INFO: Migration command completed successfully  
[2025-07-22 13:31:25] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 13:31:25] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:31:31] local.INFO: WordPress API connection validated  
[2025-07-22 13:31:31] local.INFO: Database connection validated  
[2025-07-22 13:31:31] local.INFO: Starting services migration  
[2025-07-22 13:31:31] local.INFO: Running in DRY RUN mode  
[2025-07-22 13:31:31] local.INFO: Starting services migration for clinic 1  
[2025-07-22 13:31:31] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 13:31:37] local.INFO: Found 3 services for clinic 1  
[2025-07-22 13:31:37] local.INFO: DRY RUN: Would migrate service ID 17: GP Services (Price: 33, Category ID: 1)  
[2025-07-22 13:31:37] local.INFO: DRY RUN: Would migrate service ID 60: GP Consultation (Virtual) Test (Price: 10, Category ID: 9)  
[2025-07-22 13:31:37] local.INFO: DRY RUN: Would migrate service ID 215: Same Day GP (Price: 59, Category ID: 4)  
[2025-07-22 13:31:37] local.INFO: Clinic 1 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 13:31:37] local.INFO: Migration completed - Processed: 3, Skipped: 0, Errors: 0  
[2025-07-22 13:31:37] local.INFO: Migration command completed successfully  
[2025-07-22 13:31:45] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 13:31:45] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:31:50] local.INFO: WordPress API connection validated  
[2025-07-22 13:31:50] local.INFO: Database connection validated  
[2025-07-22 13:31:50] local.INFO: Starting services migration  
[2025-07-22 13:31:50] local.INFO: Starting services migration for clinic 1  
[2025-07-22 13:31:50] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 13:31:57] local.INFO: Found 3 services for clinic 1  
[2025-07-22 13:31:57] local.INFO: Successfully migrated service ID 17 → Laravel ID 6: GP Services  
[2025-07-22 13:31:57] local.INFO: Successfully migrated service ID 60 → Laravel ID 7: GP Consultation (Virtual) Test  
[2025-07-22 13:31:57] local.INFO: Successfully migrated service ID 215 → Laravel ID 8: Same Day GP  
[2025-07-22 13:31:57] local.INFO: Clinic 1 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 13:31:57] local.INFO: Migration completed - Processed: 3, Skipped: 0, Errors: 0  
[2025-07-22 13:31:57] local.INFO: Migration command completed successfully  
[2025-07-22 14:57:41] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 14:57:41] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 14:57:49] local.INFO: WordPress API connection validated  
[2025-07-22 14:57:49] local.INFO: Database connection validated  
[2025-07-22 14:57:49] local.INFO: Starting services migration  
[2025-07-22 14:57:49] local.INFO: Running in DRY RUN mode  
[2025-07-22 14:57:49] local.INFO: Starting services migration for clinic 1  
[2025-07-22 14:57:49] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:57:54] local.INFO: Found 3 services for clinic 1  
[2025-07-22 14:57:54] local.INFO: DRY RUN: Would migrate service ID 17: GP Services (Price: 33, Category ID: 1)  
[2025-07-22 14:57:54] local.INFO: DRY RUN: Would migrate service ID 60: GP Consultation (Virtual) Test (Price: 10, Category ID: 9)  
[2025-07-22 14:57:54] local.INFO: DRY RUN: Would migrate service ID 215: Same Day GP (Price: 59, Category ID: 4)  
[2025-07-22 14:57:54] local.INFO: Clinic 1 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 14:57:54] local.INFO: Migration completed - Processed: 3, Skipped: 0, Errors: 0  
[2025-07-22 14:57:54] local.INFO: Migration command completed successfully  
[2025-07-22 14:58:08] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 14:58:08] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 14:58:13] local.INFO: WordPress API connection validated  
[2025-07-22 14:58:13] local.INFO: Database connection validated  
[2025-07-22 14:58:13] local.INFO: Starting services migration  
[2025-07-22 14:58:13] local.INFO: Starting services migration for clinic 1  
[2025-07-22 14:58:13] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:58:17] local.INFO: Found 3 services for clinic 1  
[2025-07-22 14:58:18] local.INFO: Successfully migrated service ID 17 → Laravel ID 6: GP Services  
[2025-07-22 14:58:18] local.INFO: Successfully migrated service ID 60 → Laravel ID 7: GP Consultation (Virtual) Test  
[2025-07-22 14:58:18] local.INFO: Successfully migrated service ID 215 → Laravel ID 8: Same Day GP  
[2025-07-22 14:58:18] local.INFO: Clinic 1 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 14:58:18] local.INFO: Migration completed - Processed: 3, Skipped: 0, Errors: 0  
[2025-07-22 14:58:18] local.INFO: Migration command completed successfully  
[2025-07-22 14:58:27] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 14:58:27] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 14:58:32] local.INFO: WordPress API connection validated  
[2025-07-22 14:58:32] local.INFO: Database connection validated  
[2025-07-22 14:58:32] local.INFO: Starting services migration  
[2025-07-22 14:58:32] local.INFO: Running in DRY RUN mode  
[2025-07-22 14:58:32] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 14:58:38] local.INFO: Starting services migration for clinic 40  
[2025-07-22 14:58:38] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:58:44] local.INFO: Found 3 services for clinic 40  
[2025-07-22 14:58:44] local.INFO: DRY RUN: Would migrate service ID 320: Medication (Price: 200, Category ID: 4)  
[2025-07-22 14:58:44] local.INFO: DRY RUN: Would migrate service ID 321: Blood Tests (Price: 150, Category ID: 39)  
[2025-07-22 14:58:44] local.INFO: DRY RUN: Would migrate service ID 322: Virtual consultation (Price: 120, Category ID: 4)  
[2025-07-22 14:58:44] local.INFO: Clinic 40 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 14:58:44] local.INFO: Starting services migration for clinic 27  
[2025-07-22 14:58:44] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:58:49] local.INFO: No services found for clinic 27  
[2025-07-22 14:58:49] local.INFO: Starting services migration for clinic 26  
[2025-07-22 14:58:49] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:58:53] local.INFO: No services found for clinic 26  
[2025-07-22 14:58:53] local.INFO: Starting services migration for clinic 22  
[2025-07-22 14:58:53] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:59:00] local.INFO: No services found for clinic 22  
[2025-07-22 14:59:00] local.INFO: Starting services migration for clinic 16  
[2025-07-22 14:59:00] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:59:04] local.INFO: Found 3 services for clinic 16  
[2025-07-22 14:59:04] local.INFO: DRY RUN: Would migrate service ID 195: Blood Pressure Check (Price: 20, Category ID: 4)  
[2025-07-22 14:59:04] local.INFO: DRY RUN: Would migrate service ID 230: PCP Appointment (Price: 200, Category ID: 9)  
[2025-07-22 14:59:04] local.INFO: DRY RUN: Would migrate service ID 231: Cyst Removal (Price: 150, Category ID: 9)  
[2025-07-22 14:59:04] local.INFO: Clinic 16 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 14:59:04] local.INFO: Starting services migration for clinic 15  
[2025-07-22 14:59:04] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:59:09] local.INFO: Found 2 services for clinic 15  
[2025-07-22 14:59:09] local.INFO: DRY RUN: Would migrate service ID 110: Urgent Care (In-Person) (Price: 100, Category ID: 14)  
[2025-07-22 14:59:09] local.INFO: DRY RUN: Would migrate service ID 111: Urgent Care (Virtual) (Price: 75, Category ID: 14)  
[2025-07-22 14:59:09] local.INFO: Clinic 15 completed: 2 processed, 0 skipped, 0 errors  
[2025-07-22 14:59:09] local.INFO: Starting services migration for clinic 14  
[2025-07-22 14:59:09] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:59:15] local.INFO: Found 136 services for clinic 14  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 68: Dressing Clinic (Price: 75, Category ID: 48)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 69: STI Check - Women only (Price: 120, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 77: Period Clinic (Menstrual Disorders) (Price: 250, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 78: Pelvic Pain (Price: 250, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 79: Menopause (Price: 250, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 80: Post-reproductive Women's Health (Price: 250, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 81: General Womens Health (Price: 250, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 82: Vulval and Vaginal Health (Price: 250, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 84: Recurrent Miscarriages (Price: 250, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 90: Coil Fitting (Price: 500, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 98: Recurrent Miscarriage Testing (Price: 750, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 99: Follicle Tracking Scan (Price: 160, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 103: Vulvoscopy (Price: 675, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 107: Pelvic Ultrasound (Consultant) (Price: 300, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 112: Private GP - In Person (Price: 100, Category ID: 4)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 119: Private GP Virtual (Price: 75, Category ID: 4)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 121: Weight Loss (Price: 225, Category ID: 2)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 122: Weight Loss (Online) (Price: 225, Category ID: 2)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 124: Urgent Care (Virtual) (Price: 75, Category ID: 14)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 125: Menopausal Health Check (Price: 375, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 126: Coil Removal (Price: 250, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 127: Pre-Pregnancy Health Check (Price: 400, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 128: Ovarian Cancer Screening (Price: 550, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 129: Recurrent Thrush (Price: 250, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 130: PCOS Consultation (Price: 250, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 131: Endometriosis (Price: 250, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 132: Post Natal Care (Price: 250, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 133: Fibroids Consultation (Price: 250, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 134: Fertility Health Check (Price: 450, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 135: Mini Sexual Health Check (Price: 350, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 136: Comprehensive Sexual Health Check (Price: 500, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 138: General Health Check (Price: 300, Category ID: 20)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 146: Basic Health Check (Price: 199, Category ID: 20)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 147: Over 50 Men's Health Check (Price: 295, Category ID: 20)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 148: Over 50 Women's Health Check (Price: 350, Category ID: 20)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 149: Heart Health Check (Price: 375, Category ID: 20)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 150: Comprehensive Health Check (Price: 475, Category ID: 20)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 154: Cyst Removal (Price: 300, Category ID: 21)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 158: Smear Test (Price: 180, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 160: Urgent Care Follow-Up (In-Person ) (Price: 75, Category ID: 14)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 169: Ultrasound Foot and Ankle (Price: 175, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 170: Shoulder and Upper Arm Scan (Price: 150, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 181: Upper Abdominal Scan (Price: 53, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 182: Thyroid Scan (Price: 94, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 189: Abdominal Aorta Scan (Price: 100, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 196: Wound Swab (Price: 70, Category ID: 48)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 198: Knee Scan (Price: 175, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 203: Follow-Up GP Appointment (Price: 75, Category ID: 4)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 204: Phlebotomy (Blood Draw) (Price: 25, Category ID: 44)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 206: Aesthetics Consultation (Redeemable) (Price: 50, Category ID: 17)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 216: Skin Specialist GP Consultation (Price: 120, Category ID: 21)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 220: Gynaecology New Consultation (Price: 250, Category ID: 27)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 221: Gynaecology Follow-up (Price: 175, Category ID: 27)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 235: Urology New Consultation (Price: 250, Category ID: 27)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 239: Durolane Injections (Price: 300, Category ID: 29)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 240: Ostenil Plus Injections (Price: 250, Category ID: 29)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 241: Platelet-Rich Plasma (PRP) (Price: 400, Category ID: 29)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 243: Respiratory Follow-up Consultation (Price: 175, Category ID: 27)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 244: Heart Health Consultation (Price: 250, Category ID: 27)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 245: Men's Health Consultation (Price: 100, Category ID: 4)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 247: Drivers Medicals (Price: 75, Category ID: 4)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 260: Pelvic Scan - Well women (Price: 120, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 262: Sexual Health Screen 3 (7 STD Swabs) (Price: 140, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 263: Sexual Health Screen 2 (Blood tests and Swab) (Price: 120, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 266: Sexual Health Screen 1 (Chlamydia+Gonorrhoea) (Price: 75, Category ID: 20)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 267: Initial consultation - Skin Procedures (Price: 75, Category ID: 21)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 269: Flexible Cystoscopy (Price: 725, Category ID: 44)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 274: Fertility - Monitoring Scan (Price: 165, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 275: Fertility - Baseline Scan (Price: 300, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 278: Breast with Armpit (Both Sides) (Price: 300, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 279: Tetanus Shot (Price: 50, Category ID: 14)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 280: Sexual Health Screen 3 (7 STD Swabs) (Price: 175, Category ID: 44)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 281: Prostate Scan (Price: 175, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 282: Testicular Scan (Price: 160, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 286: Breast with Armpit (Price: 175, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 287: Wrist and Hand (Price: 175, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 289: Hip Scan (Price: 175, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 290: Ankle and Foot Scan (Price: 175, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 291: Fertility - Endometrial Thickness (Price: 90, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 299: ECG (Price: 60, Category ID: 44)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 302: Upper Abdominal and pelvic scan (Price: 200, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 308: Weight Loss Consultation (virtual) (Price: 89, Category ID: 4)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 317: General Surgery Consultation (Price: 275, Category ID: 27)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 318: General Surgery Follow Up (Price: 175, Category ID: 27)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 327: Urology Follow-up (Price: 200, Category ID: 27)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 337: Steroid Injections (Price: 200, Category ID: 29)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 339: Fertility Counselling (Price: 120, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 345: Women's Health Specialist Consultation (Price: 120, Category ID: 14)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 349: Echocardiogram (Heart Scan) (Price: 400, Category ID: 44)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 353: Neck Scan (Price: 175, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 354: Heart New Appointment (Price: 275, Category ID: 27)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 355: Heart Follow-up (Price: 200, Category ID: 27)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 366: Virtual Women's Health Specialist Consultation (Price: 100, Category ID: 4)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 368: Urgent Care - In-Person (Price: 100, Category ID: 14)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 373: Blood Tests (Price: 100, Category ID: 5)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 374: Blood Tests (Price: 100, Category ID: 5)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 378: Blood Tests (Price: 100, Category ID: 5)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 379: Urgent Care (In-Person) (Price: 100, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 380: Private GP - In Person (Price: 100, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 381: Urgent Care (Virtual) (Price: 75, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 382: Ear Microsuction (Both ears) (Price: 45, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 383: Cyst Removal (Price: 300, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 384: Soft Tissue Lumps and Bumps Scan (Price: 350, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 386: Doppler Lower Limb (One Side) (Price: 100, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 387: Sexual Health Screen 3 (7 STD Swabs) (Price: 175, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 388: Phlebotomy (Price: 210, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 389: Blood test (Price: 210, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 390: ECG (Price: 60, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 391: Blood test referral (Price: 72, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 392: Medication (Price: 200, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 393: Blood Tests (Price: 150, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 394: WEIGHT LOSS MEDICATION (Price: 0, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 395: MRI Scan (One Part) (Price: 380, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 396: Medication (Price: 275, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 397: Medication (Price: 25, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 398: Medication (Price: 25, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 401: Blood Test (Price: 75, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 402: Blood Test (Price: 75, Category ID: )  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 406: Recurrent Thrush Package (Price: 750, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 407: Urgent Care (In-Person) (Price: 100, Category ID: 14)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 408: Private GP - In Person (Price: 75, Category ID: 4)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 409: Urgent Care (Virtual) (Price: 400, Category ID: 18)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 410: Cyst Removal (Price: 300, Category ID: 21)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 411: Soft Tissue Lumps and Bumps Scan (Price: 350, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 413: Doppler Lower Limb (One Side) (Price: 100, Category ID: 5)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 414: Sexual Health Screen 3 (7 STD Swabs) (Price: 175, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 415: Phlebotomy (Price: 210, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 416: Blood test (Price: 210, Category ID: 22)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 417: Blood test referral (Price: 72, Category ID: 5)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 418: Medication (Price: 200, Category ID: 4)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 419: Blood Tests (Price: 150, Category ID: 39)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 420: WEIGHT LOSS MEDICATION (Price: 0, Category ID: 21)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 421: MRI Scan (One Part) (Price: 380, Category ID: 42)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 422: Medication (Price: 275, Category ID: 9)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 423: Medication (Price: 25, Category ID: 43)  
[2025-07-22 14:59:15] local.INFO: DRY RUN: Would migrate service ID 424: Medication (Price: 25, Category ID: 43)  
[2025-07-22 14:59:15] local.INFO: Clinic 14 completed: 136 processed, 0 skipped, 0 errors  
[2025-07-22 14:59:15] local.INFO: Starting services migration for clinic 13  
[2025-07-22 14:59:15] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:59:21] local.INFO: Found 1 services for clinic 13  
[2025-07-22 14:59:21] local.INFO: DRY RUN: Would migrate service ID 39: Medical Assessment (Price: 60, Category ID: 4)  
[2025-07-22 14:59:21] local.INFO: Clinic 13 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 14:59:21] local.INFO: Starting services migration for clinic 12  
[2025-07-22 14:59:21] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:59:26] local.INFO: Found 12 services for clinic 12  
[2025-07-22 14:59:26] local.INFO: DRY RUN: Would migrate service ID 36: Dermatology consultation (Price: 95, Category ID: 4)  
[2025-07-22 14:59:26] local.INFO: DRY RUN: Would migrate service ID 228: Repeat Prescriptions only (will require proof of prescription) (Price: 30, Category ID: 4)  
[2025-07-22 14:59:26] local.INFO: DRY RUN: Would migrate service ID 305: Blood tests (Price: 38, Category ID: 5)  
[2025-07-22 14:59:26] local.INFO: DRY RUN: Would migrate service ID 306: Video/Telephone GP appointment (Standard) (Price: 55, Category ID: 4)  
[2025-07-22 14:59:26] local.INFO: DRY RUN: Would migrate service ID 307: Video/Telephone GP appointment (long) (Price: 89, Category ID: 4)  
[2025-07-22 14:59:26] local.INFO: DRY RUN: Would migrate service ID 309: Referral letter (with standard consultation) (Price: 89, Category ID: 4)  
[2025-07-22 14:59:26] local.INFO: DRY RUN: Would migrate service ID 310: Sick note / Medical certificate (Price: 89, Category ID: 4)  
[2025-07-22 14:59:26] local.INFO: DRY RUN: Would migrate service ID 311: Women's Health consultation (Price: 94, Category ID: 4)  
[2025-07-22 14:59:26] local.INFO: DRY RUN: Would migrate service ID 312: Mental health consultation (Price: 96, Category ID: 4)  
[2025-07-22 14:59:26] local.INFO: DRY RUN: Would migrate service ID 404: Weight loss Injection enquiry - Mounjaro (Price: 0, Category ID: 4)  
[2025-07-22 14:59:26] local.INFO: DRY RUN: Would migrate service ID 405: Weight loss injections (Price: 0, Category ID: 2)  
[2025-07-22 14:59:26] local.INFO: DRY RUN: Would migrate service ID 425: Aesthetics Initial consultation (Price: 13, Category ID: 45)  
[2025-07-22 14:59:26] local.INFO: Clinic 12 completed: 12 processed, 0 skipped, 0 errors  
[2025-07-22 14:59:26] local.INFO: Starting services migration for clinic 11  
[2025-07-22 14:59:26] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:59:32] local.INFO: Found 7 services for clinic 11  
[2025-07-22 14:59:32] local.INFO: DRY RUN: Would migrate service ID 7: ⁠Urgent Telephone or Video consultation on request (Price: 85, Category ID: 4)  
[2025-07-22 14:59:32] local.INFO: DRY RUN: Would migrate service ID 8: ⁠NMC health declaration Video consultation (Price: 85, Category ID: 4)  
[2025-07-22 14:59:32] local.INFO: DRY RUN: Would migrate service ID 9: Menopause consultation ( initial appointment) (Price: 85, Category ID: 4)  
[2025-07-22 14:59:32] local.INFO: DRY RUN: Would migrate service ID 64: Mental health consultation (Initial appointment) (Price: 85, Category ID: 4)  
[2025-07-22 14:59:32] local.INFO: DRY RUN: Would migrate service ID 350: Prostate Health consultation +PSA (Price: 150, Category ID: 4)  
[2025-07-22 14:59:32] local.INFO: DRY RUN: Would migrate service ID 351: Virtual consultation (Price: 85, Category ID: 4)  
[2025-07-22 14:59:32] local.INFO: DRY RUN: Would migrate service ID 352: Telephone or Video consultation (standard) (Price: 55, Category ID: 4)  
[2025-07-22 14:59:32] local.INFO: Clinic 11 completed: 7 processed, 0 skipped, 0 errors  
[2025-07-22 14:59:32] local.INFO: Starting services migration for clinic 10  
[2025-07-22 14:59:32] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:59:38] local.INFO: Found 2 services for clinic 10  
[2025-07-22 14:59:38] local.INFO: DRY RUN: Would migrate service ID 61: General Consultations (Price: 60, Category ID: 4)  
[2025-07-22 14:59:38] local.INFO: DRY RUN: Would migrate service ID 62: Weight Loss (Price: 120, Category ID: 4)  
[2025-07-22 14:59:38] local.INFO: Clinic 10 completed: 2 processed, 0 skipped, 0 errors  
[2025-07-22 14:59:38] local.INFO: Starting services migration for clinic 9  
[2025-07-22 14:59:38] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:59:42] local.INFO: Found 1 services for clinic 9  
[2025-07-22 14:59:42] local.INFO: DRY RUN: Would migrate service ID 30: Initial consultation (Price: 100, Category ID: 4)  
[2025-07-22 14:59:42] local.INFO: Clinic 9 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 14:59:42] local.INFO: Starting services migration for clinic 8  
[2025-07-22 14:59:42] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:59:47] local.INFO: Found 8 services for clinic 8  
[2025-07-22 14:59:47] local.INFO: DRY RUN: Would migrate service ID 12: Initial GP consultation (Price: 90, Category ID: 4)  
[2025-07-22 14:59:47] local.INFO: DRY RUN: Would migrate service ID 49: Virtual (Price: 75, Category ID: 4)  
[2025-07-22 14:59:47] local.INFO: DRY RUN: Would migrate service ID 144: Virtual Follow-up (Price: 65, Category ID: 9)  
[2025-07-22 14:59:47] local.INFO: DRY RUN: Would migrate service ID 145: Phlebotomy (Price: 50, Category ID: 5)  
[2025-07-22 14:59:47] local.INFO: DRY RUN: Would migrate service ID 168: Wellman Prostate health check (Price: 90, Category ID: 44)  
[2025-07-22 14:59:47] local.INFO: DRY RUN: Would migrate service ID 171: GP Follow-up (Price: 75, Category ID: 9)  
[2025-07-22 14:59:47] local.INFO: DRY RUN: Would migrate service ID 172: Cardiovascular / Diabetes Health check (Price: 350, Category ID: 4)  
[2025-07-22 14:59:47] local.INFO: DRY RUN: Would migrate service ID 173: Soft Tissue Lumps and Bumps Scan (Price: 350, Category ID: 22)  
[2025-07-22 14:59:47] local.INFO: Clinic 8 completed: 8 processed, 0 skipped, 0 errors  
[2025-07-22 14:59:47] local.INFO: Starting services migration for clinic 6  
[2025-07-22 14:59:47] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:59:51] local.INFO: Found 1 services for clinic 6  
[2025-07-22 14:59:51] local.INFO: DRY RUN: Would migrate service ID 142: Video Consultation (Price: 500, Category ID: 9)  
[2025-07-22 14:59:51] local.INFO: Clinic 6 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 14:59:51] local.INFO: Starting services migration for clinic 5  
[2025-07-22 14:59:51] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 14:59:58] local.INFO: Found 23 services for clinic 5  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 16: Initial with pen - clinic (Price: 250, Category ID: 2)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 18: Initial with pen (video) (Price: 250, Category ID: 2)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 21: 1 ear (Price: 50, Category ID: 10)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 22: Minor illness - video (Price: 65, Category ID: 9)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 23: follow up with pen - video (Price: 225, Category ID: 2)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 24: follow up with pen -clinic (Price: 225, Category ID: 2)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 25: Minor illness -clinic (Price: 65, Category ID: 9)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 40: both ears (Price: 70, Category ID: 10)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 41: Dressing -simple (Price: 45, Category ID: 11)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 42: Dressing- complex (Price: 60, Category ID: 11)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 43: Suture/Clip removal (Price: 45, Category ID: 11)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 44: own medication (Price: 25, Category ID: 12)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 45: B12 (Price: 30, Category ID: 12)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 46: biotin (Price: 60, Category ID: 12)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 47: own form (Price: 35, Category ID: 13)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 249: IV administration only (Price: 80, Category ID: 31)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 357: Video Follow up - 7.5mg/10mg (Price: 235, Category ID: 2)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 358: Video Follow up - 12.5mg/15mg (Price: 250, Category ID: 2)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 359: In-Clinic Follow up - 2.5mg/5mg (Price: 225, Category ID: 2)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 360: In-Clinic Follow up - 5mg/7.5mg (Price: 235, Category ID: 2)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 361: In-Clinic Follow up - 12.5mg/15mg (Price: 250, Category ID: 2)  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 400: Video Follow up - 2.5mg/5mg (Price: 175, Category ID: )  
[2025-07-22 14:59:58] local.INFO: DRY RUN: Would migrate service ID 426: Video Follow up - 2.5mg/5mg (Price: 178, Category ID: 22)  
[2025-07-22 14:59:58] local.INFO: Clinic 5 completed: 23 processed, 0 skipped, 0 errors  
[2025-07-22 14:59:58] local.INFO: Starting services migration for clinic 4  
[2025-07-22 14:59:58] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:00:04] local.INFO: No services found for clinic 4  
[2025-07-22 15:00:04] local.INFO: Starting services migration for clinic 2  
[2025-07-22 15:00:04] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:00:09] local.INFO: No services found for clinic 2  
[2025-07-22 15:00:09] local.INFO: Starting services migration for clinic 1  
[2025-07-22 15:00:09] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:00:15] local.INFO: Found 3 services for clinic 1  
[2025-07-22 15:00:15] local.INFO: DRY RUN: Would migrate service ID 17: GP Services (Price: 33, Category ID: 1)  
[2025-07-22 15:00:15] local.INFO: DRY RUN: Would migrate service ID 60: GP Consultation (Virtual) Test (Price: 10, Category ID: 9)  
[2025-07-22 15:00:15] local.INFO: DRY RUN: Would migrate service ID 215: Same Day GP (Price: 59, Category ID: 4)  
[2025-07-22 15:00:15] local.INFO: Clinic 1 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 15:00:15] local.INFO: Migration completed - Processed: 202, Skipped: 0, Errors: 0  
[2025-07-22 15:00:15] local.INFO: Migration command completed successfully  
[2025-07-22 15:01:20] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 15:01:20] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 15:01:25] local.INFO: WordPress API connection validated  
[2025-07-22 15:01:25] local.INFO: Database connection validated  
[2025-07-22 15:01:25] local.INFO: Starting services migration  
[2025-07-22 15:01:25] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 15:01:30] local.INFO: Starting services migration for clinic 40  
[2025-07-22 15:01:30] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:01:35] local.INFO: Found 3 services for clinic 40  
[2025-07-22 15:01:35] local.INFO: Clinic 40 completed: 0 processed, 0 skipped, 3 errors  
[2025-07-22 15:01:35] local.INFO: Starting services migration for clinic 27  
[2025-07-22 15:01:35] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:01:40] local.INFO: No services found for clinic 27  
[2025-07-22 15:01:40] local.INFO: Starting services migration for clinic 26  
[2025-07-22 15:01:40] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:01:45] local.INFO: No services found for clinic 26  
[2025-07-22 15:01:45] local.INFO: Starting services migration for clinic 22  
[2025-07-22 15:01:45] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:01:50] local.INFO: No services found for clinic 22  
[2025-07-22 15:01:50] local.INFO: Starting services migration for clinic 16  
[2025-07-22 15:01:50] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:01:55] local.INFO: Found 3 services for clinic 16  
[2025-07-22 15:01:55] local.INFO: Successfully migrated service ID 195 → Laravel ID 12: Blood Pressure Check  
[2025-07-22 15:01:55] local.INFO: Successfully migrated service ID 230 → Laravel ID 13: PCP Appointment  
[2025-07-22 15:01:55] local.INFO: Successfully migrated service ID 231 → Laravel ID 14: Cyst Removal  
[2025-07-22 15:01:55] local.INFO: Clinic 16 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 15:01:55] local.INFO: Starting services migration for clinic 15  
[2025-07-22 15:01:55] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:03:16] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 15:03:16] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 15:03:20] local.INFO: WordPress API connection validated  
[2025-07-22 15:03:20] local.INFO: Database connection validated  
[2025-07-22 15:03:20] local.INFO: Starting services migration  
[2025-07-22 15:03:20] local.INFO: Running in DRY RUN mode  
[2025-07-22 15:03:20] local.INFO: Starting services migration for clinic 40  
[2025-07-22 15:03:20] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:03:26] local.INFO: Found 3 services for clinic 40  
[2025-07-22 15:03:26] local.INFO: DRY RUN: Would migrate service ID 320: Medication (Price: 200, Category ID: 4)  
[2025-07-22 15:03:26] local.INFO: DRY RUN: Would migrate service ID 321: Blood Tests (Price: 150, Category ID: 39)  
[2025-07-22 15:03:26] local.INFO: DRY RUN: Would migrate service ID 322: Virtual consultation (Price: 120, Category ID: 4)  
[2025-07-22 15:03:26] local.INFO: Clinic 40 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 15:03:26] local.INFO: Migration completed - Processed: 3, Skipped: 0, Errors: 0  
[2025-07-22 15:03:26] local.INFO: Migration command completed successfully  
[2025-07-22 15:03:34] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 15:03:34] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 15:05:31] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 15:05:31] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 15:05:36] local.INFO: WordPress API connection validated  
[2025-07-22 15:05:36] local.INFO: Database connection validated  
[2025-07-22 15:05:36] local.INFO: Starting services migration  
[2025-07-22 15:05:36] local.INFO: Starting services migration for clinic 40  
[2025-07-22 15:05:36] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:05:41] local.INFO: Found 3 services for clinic 40  
[2025-07-22 15:05:41] local.INFO: Successfully migrated service ID 320 → Laravel ID 15: Medication  
[2025-07-22 15:05:41] local.INFO: Successfully migrated service ID 321 → Laravel ID 16: Blood Tests  
[2025-07-22 15:05:41] local.INFO: Successfully migrated service ID 322 → Laravel ID 17: Virtual consultation  
[2025-07-22 15:05:41] local.INFO: Clinic 40 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 15:05:41] local.INFO: Migration completed - Processed: 3, Skipped: 0, Errors: 0  
[2025-07-22 15:05:41] local.INFO: Migration command completed successfully  
[2025-07-22 15:05:56] local.INFO: Starting migration command: migratewp:services  
[2025-07-22 15:05:56] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 15:06:02] local.INFO: WordPress API connection validated  
[2025-07-22 15:06:02] local.INFO: Database connection validated  
[2025-07-22 15:06:02] local.INFO: Starting services migration  
[2025-07-22 15:06:02] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 15:06:07] local.INFO: Starting services migration for clinic 40  
[2025-07-22 15:06:07] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:06:11] local.INFO: Found 3 services for clinic 40  
[2025-07-22 15:06:11] local.INFO: Successfully migrated service ID 320 → Laravel ID 15: Medication  
[2025-07-22 15:06:11] local.INFO: Successfully migrated service ID 321 → Laravel ID 16: Blood Tests  
[2025-07-22 15:06:11] local.INFO: Successfully migrated service ID 322 → Laravel ID 17: Virtual consultation  
[2025-07-22 15:06:11] local.INFO: Clinic 40 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 15:06:11] local.INFO: Starting services migration for clinic 27  
[2025-07-22 15:06:11] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:06:16] local.INFO: No services found for clinic 27  
[2025-07-22 15:06:16] local.INFO: Starting services migration for clinic 26  
[2025-07-22 15:06:16] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:06:22] local.INFO: No services found for clinic 26  
[2025-07-22 15:06:22] local.INFO: Starting services migration for clinic 22  
[2025-07-22 15:06:22] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:06:28] local.INFO: No services found for clinic 22  
[2025-07-22 15:06:28] local.INFO: Starting services migration for clinic 16  
[2025-07-22 15:06:28] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:06:34] local.INFO: Found 3 services for clinic 16  
[2025-07-22 15:06:34] local.INFO: Successfully migrated service ID 195 → Laravel ID 12: Blood Pressure Check  
[2025-07-22 15:06:34] local.INFO: Successfully migrated service ID 230 → Laravel ID 13: PCP Appointment  
[2025-07-22 15:06:34] local.INFO: Successfully migrated service ID 231 → Laravel ID 14: Cyst Removal  
[2025-07-22 15:06:34] local.INFO: Clinic 16 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 15:06:34] local.INFO: Starting services migration for clinic 15  
[2025-07-22 15:06:34] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:06:41] local.INFO: Found 2 services for clinic 15  
[2025-07-22 15:06:41] local.INFO: Successfully migrated service ID 110 → Laravel ID 18: Urgent Care (In-Person)  
[2025-07-22 15:06:41] local.INFO: Successfully migrated service ID 111 → Laravel ID 19: Urgent Care (Virtual)  
[2025-07-22 15:06:41] local.INFO: Clinic 15 completed: 2 processed, 0 skipped, 0 errors  
[2025-07-22 15:06:41] local.INFO: Starting services migration for clinic 14  
[2025-07-22 15:06:41] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:06:46] local.INFO: Found 136 services for clinic 14  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 68 → Laravel ID 20: Dressing Clinic  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 69 → Laravel ID 21: STI Check - Women only  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 77 → Laravel ID 22: Period Clinic (Menstrual Disorders)  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 78 → Laravel ID 23: Pelvic Pain  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 79 → Laravel ID 24: Menopause  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 80 → Laravel ID 25: Post-reproductive Women's Health  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 81 → Laravel ID 26: General Womens Health  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 82 → Laravel ID 27: Vulval and Vaginal Health  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 84 → Laravel ID 28: Recurrent Miscarriages  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 90 → Laravel ID 29: Coil Fitting  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 98 → Laravel ID 30: Recurrent Miscarriage Testing  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 99 → Laravel ID 31: Follicle Tracking Scan  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 103 → Laravel ID 32: Vulvoscopy  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 107 → Laravel ID 33: Pelvic Ultrasound (Consultant)  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 112 → Laravel ID 34: Private GP - In Person  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 119 → Laravel ID 35: Private GP Virtual  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 121 → Laravel ID 36: Weight Loss  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 122 → Laravel ID 37: Weight Loss (Online)  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 124 → Laravel ID 38: Urgent Care (Virtual)  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 125 → Laravel ID 39: Menopausal Health Check  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 126 → Laravel ID 40: Coil Removal  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 127 → Laravel ID 41: Pre-Pregnancy Health Check  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 128 → Laravel ID 42: Ovarian Cancer Screening  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 129 → Laravel ID 43: Recurrent Thrush  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 130 → Laravel ID 44: PCOS Consultation  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 131 → Laravel ID 45: Endometriosis  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 132 → Laravel ID 46: Post Natal Care  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 133 → Laravel ID 47: Fibroids Consultation  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 134 → Laravel ID 48: Fertility Health Check  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 135 → Laravel ID 49: Mini Sexual Health Check  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 136 → Laravel ID 50: Comprehensive Sexual Health Check  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 138 → Laravel ID 51: General Health Check  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 146 → Laravel ID 52: Basic Health Check  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 147 → Laravel ID 53: Over 50 Men's Health Check  
[2025-07-22 15:06:46] local.INFO: Successfully migrated service ID 148 → Laravel ID 54: Over 50 Women's Health Check  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 149 → Laravel ID 55: Heart Health Check  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 150 → Laravel ID 56: Comprehensive Health Check  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 154 → Laravel ID 57: Cyst Removal  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 158 → Laravel ID 58: Smear Test  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 160 → Laravel ID 59: Urgent Care Follow-Up (In-Person )  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 169 → Laravel ID 60: Ultrasound Foot and Ankle  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 170 → Laravel ID 61: Shoulder and Upper Arm Scan  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 181 → Laravel ID 62: Upper Abdominal Scan  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 182 → Laravel ID 63: Thyroid Scan  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 189 → Laravel ID 64: Abdominal Aorta Scan  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 196 → Laravel ID 65: Wound Swab  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 198 → Laravel ID 66: Knee Scan  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 203 → Laravel ID 67: Follow-Up GP Appointment  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 204 → Laravel ID 68: Phlebotomy (Blood Draw)  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 206 → Laravel ID 69: Aesthetics Consultation (Redeemable)  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 216 → Laravel ID 70: Skin Specialist GP Consultation  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 220 → Laravel ID 71: Gynaecology New Consultation  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 221 → Laravel ID 72: Gynaecology Follow-up  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 235 → Laravel ID 73: Urology New Consultation  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 239 → Laravel ID 74: Durolane Injections  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 240 → Laravel ID 75: Ostenil Plus Injections  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 241 → Laravel ID 76: Platelet-Rich Plasma (PRP)  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 243 → Laravel ID 77: Respiratory Follow-up Consultation  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 244 → Laravel ID 78: Heart Health Consultation  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 245 → Laravel ID 79: Men's Health Consultation  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 247 → Laravel ID 80: Drivers Medicals  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 260 → Laravel ID 81: Pelvic Scan - Well women  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 262 → Laravel ID 82: Sexual Health Screen 3 (7 STD Swabs)  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 263 → Laravel ID 83: Sexual Health Screen 2 (Blood tests and Swab)  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 266 → Laravel ID 84: Sexual Health Screen 1 (Chlamydia+Gonorrhoea)  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 267 → Laravel ID 85: Initial consultation - Skin Procedures  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 269 → Laravel ID 86: Flexible Cystoscopy  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 274 → Laravel ID 87: Fertility - Monitoring Scan  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 275 → Laravel ID 88: Fertility - Baseline Scan  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 278 → Laravel ID 89: Breast with Armpit (Both Sides)  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 279 → Laravel ID 90: Tetanus Shot  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 280 → Laravel ID 91: Sexual Health Screen 3 (7 STD Swabs)  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 281 → Laravel ID 92: Prostate Scan  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 282 → Laravel ID 93: Testicular Scan  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 286 → Laravel ID 94: Breast with Armpit  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 287 → Laravel ID 95: Wrist and Hand  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 289 → Laravel ID 96: Hip Scan  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 290 → Laravel ID 97: Ankle and Foot Scan  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 291 → Laravel ID 98: Fertility - Endometrial Thickness  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 299 → Laravel ID 99: ECG  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 302 → Laravel ID 100: Upper Abdominal and pelvic scan  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 308 → Laravel ID 101: Weight Loss Consultation (virtual)  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 317 → Laravel ID 102: General Surgery Consultation  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 318 → Laravel ID 103: General Surgery Follow Up  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 327 → Laravel ID 104: Urology Follow-up  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 337 → Laravel ID 105: Steroid Injections  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 339 → Laravel ID 106: Fertility Counselling  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 345 → Laravel ID 107: Women's Health Specialist Consultation  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 349 → Laravel ID 108: Echocardiogram (Heart Scan)  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 353 → Laravel ID 109: Neck Scan  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 354 → Laravel ID 110: Heart New Appointment  
[2025-07-22 15:06:47] local.INFO: Successfully migrated service ID 355 → Laravel ID 111: Heart Follow-up  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 366 → Laravel ID 112: Virtual Women's Health Specialist Consultation  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 368 → Laravel ID 113: Urgent Care - In-Person  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 373 → Laravel ID 114: Blood Tests  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 374 → Laravel ID 115: Blood Tests  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 378 → Laravel ID 116: Blood Tests  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 379 → Laravel ID 117: Urgent Care (In-Person)  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 380 → Laravel ID 118: Private GP - In Person  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 381 → Laravel ID 119: Urgent Care (Virtual)  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 382 → Laravel ID 120: Ear Microsuction (Both ears)  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 383 → Laravel ID 121: Cyst Removal  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 384 → Laravel ID 122: Soft Tissue Lumps and Bumps Scan  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 386 → Laravel ID 123: Doppler Lower Limb (One Side)  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 387 → Laravel ID 124: Sexual Health Screen 3 (7 STD Swabs)  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 388 → Laravel ID 125: Phlebotomy  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 389 → Laravel ID 126: Blood test  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 390 → Laravel ID 127: ECG  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 391 → Laravel ID 128: Blood test referral  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 392 → Laravel ID 129: Medication  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 393 → Laravel ID 130: Blood Tests  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 394 → Laravel ID 131: WEIGHT LOSS MEDICATION  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 395 → Laravel ID 132: MRI Scan (One Part)  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 396 → Laravel ID 133: Medication  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 397 → Laravel ID 134: Medication  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 398 → Laravel ID 135: Medication  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 401 → Laravel ID 136: Blood Test  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 402 → Laravel ID 137: Blood Test  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 406 → Laravel ID 138: Recurrent Thrush Package  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 407 → Laravel ID 139: Urgent Care (In-Person)  
[2025-07-22 15:06:48] local.INFO: Provider mapping failed: WP doctor ID 195 not found in Laravel users/providers  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 408 → Laravel ID 140: Private GP - In Person  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 409 → Laravel ID 141: Urgent Care (Virtual)  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 410 → Laravel ID 142: Cyst Removal  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 411 → Laravel ID 143: Soft Tissue Lumps and Bumps Scan  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 413 → Laravel ID 144: Doppler Lower Limb (One Side)  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 414 → Laravel ID 145: Sexual Health Screen 3 (7 STD Swabs)  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 415 → Laravel ID 146: Phlebotomy  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 416 → Laravel ID 147: Blood test  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 417 → Laravel ID 148: Blood test referral  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 418 → Laravel ID 149: Medication  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 419 → Laravel ID 150: Blood Tests  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 420 → Laravel ID 151: WEIGHT LOSS MEDICATION  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 421 → Laravel ID 152: MRI Scan (One Part)  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 422 → Laravel ID 153: Medication  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 423 → Laravel ID 154: Medication  
[2025-07-22 15:06:48] local.INFO: Successfully migrated service ID 424 → Laravel ID 155: Medication  
[2025-07-22 15:06:48] local.INFO: Clinic 14 completed: 136 processed, 0 skipped, 0 errors  
[2025-07-22 15:06:48] local.INFO: Starting services migration for clinic 13  
[2025-07-22 15:06:48] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:06:54] local.INFO: Found 1 services for clinic 13  
[2025-07-22 15:06:54] local.INFO: Successfully migrated service ID 39 → Laravel ID 156: Medical Assessment  
[2025-07-22 15:06:54] local.INFO: Clinic 13 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 15:06:54] local.INFO: Starting services migration for clinic 12  
[2025-07-22 15:06:54] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:07:00] local.INFO: Found 12 services for clinic 12  
[2025-07-22 15:07:00] local.INFO: Successfully migrated service ID 36 → Laravel ID 157: Dermatology consultation  
[2025-07-22 15:07:00] local.INFO: Successfully migrated service ID 228 → Laravel ID 158: Repeat Prescriptions only (will require proof of prescription)  
[2025-07-22 15:07:00] local.INFO: Successfully migrated service ID 305 → Laravel ID 159: Blood tests  
[2025-07-22 15:07:00] local.INFO: Successfully migrated service ID 306 → Laravel ID 160: Video/Telephone GP appointment (Standard)  
[2025-07-22 15:07:00] local.INFO: Successfully migrated service ID 307 → Laravel ID 161: Video/Telephone GP appointment (long)  
[2025-07-22 15:07:00] local.INFO: Successfully migrated service ID 309 → Laravel ID 162: Referral letter (with standard consultation)  
[2025-07-22 15:07:00] local.INFO: Successfully migrated service ID 310 → Laravel ID 163: Sick note / Medical certificate  
[2025-07-22 15:07:00] local.INFO: Successfully migrated service ID 311 → Laravel ID 164: Women's Health consultation  
[2025-07-22 15:07:00] local.INFO: Successfully migrated service ID 312 → Laravel ID 165: Mental health consultation  
[2025-07-22 15:07:00] local.INFO: Successfully migrated service ID 404 → Laravel ID 166: Weight loss Injection enquiry - Mounjaro  
[2025-07-22 15:07:00] local.INFO: Successfully migrated service ID 405 → Laravel ID 167: Weight loss injections  
[2025-07-22 15:07:00] local.INFO: Successfully migrated service ID 425 → Laravel ID 168: Aesthetics Initial consultation  
[2025-07-22 15:07:00] local.INFO: Clinic 12 completed: 12 processed, 0 skipped, 0 errors  
[2025-07-22 15:07:00] local.INFO: Starting services migration for clinic 11  
[2025-07-22 15:07:00] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:07:05] local.INFO: Found 7 services for clinic 11  
[2025-07-22 15:07:05] local.INFO: Successfully migrated service ID 7 → Laravel ID 169: ⁠Urgent Telephone or Video consultation on request  
[2025-07-22 15:07:05] local.INFO: Successfully migrated service ID 8 → Laravel ID 170: ⁠NMC health declaration Video consultation  
[2025-07-22 15:07:05] local.INFO: Successfully migrated service ID 9 → Laravel ID 171: Menopause consultation ( initial appointment)  
[2025-07-22 15:07:05] local.INFO: Successfully migrated service ID 64 → Laravel ID 172: Mental health consultation (Initial appointment)  
[2025-07-22 15:07:05] local.INFO: Successfully migrated service ID 350 → Laravel ID 173: Prostate Health consultation +PSA  
[2025-07-22 15:07:05] local.INFO: Successfully migrated service ID 351 → Laravel ID 174: Virtual consultation  
[2025-07-22 15:07:05] local.INFO: Successfully migrated service ID 352 → Laravel ID 175: Telephone or Video consultation (standard)  
[2025-07-22 15:07:05] local.INFO: Clinic 11 completed: 7 processed, 0 skipped, 0 errors  
[2025-07-22 15:07:05] local.INFO: Starting services migration for clinic 10  
[2025-07-22 15:07:05] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:07:11] local.INFO: Found 2 services for clinic 10  
[2025-07-22 15:07:11] local.INFO: Successfully migrated service ID 61 → Laravel ID 176: General Consultations  
[2025-07-22 15:07:11] local.INFO: Successfully migrated service ID 62 → Laravel ID 177: Weight Loss  
[2025-07-22 15:07:11] local.INFO: Clinic 10 completed: 2 processed, 0 skipped, 0 errors  
[2025-07-22 15:07:11] local.INFO: Starting services migration for clinic 9  
[2025-07-22 15:07:11] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:07:16] local.INFO: Found 1 services for clinic 9  
[2025-07-22 15:07:16] local.INFO: Successfully migrated service ID 30 → Laravel ID 178: Initial consultation  
[2025-07-22 15:07:16] local.INFO: Clinic 9 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 15:07:16] local.INFO: Starting services migration for clinic 8  
[2025-07-22 15:07:16] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:07:21] local.INFO: Found 8 services for clinic 8  
[2025-07-22 15:07:21] local.INFO: Successfully migrated service ID 12 → Laravel ID 179: Initial GP consultation  
[2025-07-22 15:07:21] local.INFO: Successfully migrated service ID 49 → Laravel ID 180: Virtual  
[2025-07-22 15:07:21] local.INFO: Successfully migrated service ID 144 → Laravel ID 181: Virtual Follow-up  
[2025-07-22 15:07:21] local.INFO: Successfully migrated service ID 145 → Laravel ID 182: Phlebotomy  
[2025-07-22 15:07:22] local.INFO: Successfully migrated service ID 168 → Laravel ID 183: Wellman Prostate health check  
[2025-07-22 15:07:22] local.INFO: Successfully migrated service ID 171 → Laravel ID 184: GP Follow-up  
[2025-07-22 15:07:22] local.INFO: Successfully migrated service ID 172 → Laravel ID 185: Cardiovascular / Diabetes Health check  
[2025-07-22 15:07:22] local.INFO: Successfully migrated service ID 173 → Laravel ID 186: Soft Tissue Lumps and Bumps Scan  
[2025-07-22 15:07:22] local.INFO: Clinic 8 completed: 8 processed, 0 skipped, 0 errors  
[2025-07-22 15:07:22] local.INFO: Starting services migration for clinic 6  
[2025-07-22 15:07:22] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:07:27] local.INFO: Found 1 services for clinic 6  
[2025-07-22 15:07:27] local.INFO: Successfully migrated service ID 142 → Laravel ID 187: Video Consultation  
[2025-07-22 15:07:27] local.INFO: Clinic 6 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 15:07:27] local.INFO: Starting services migration for clinic 5  
[2025-07-22 15:07:27] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:07:33] local.INFO: Found 23 services for clinic 5  
[2025-07-22 15:07:33] local.INFO: Successfully migrated service ID 16 → Laravel ID 188: Initial with pen - clinic  
[2025-07-22 15:07:33] local.INFO: Successfully migrated service ID 18 → Laravel ID 189: Initial with pen (video)  
[2025-07-22 15:07:33] local.INFO: Successfully migrated service ID 21 → Laravel ID 190: 1 ear  
[2025-07-22 15:07:33] local.INFO: Successfully migrated service ID 22 → Laravel ID 191: Minor illness - video  
[2025-07-22 15:07:33] local.INFO: Successfully migrated service ID 23 → Laravel ID 192: follow up with pen - video  
[2025-07-22 15:07:33] local.INFO: Successfully migrated service ID 24 → Laravel ID 193: follow up with pen -clinic  
[2025-07-22 15:07:33] local.INFO: Successfully migrated service ID 25 → Laravel ID 194: Minor illness -clinic  
[2025-07-22 15:07:33] local.INFO: Successfully migrated service ID 40 → Laravel ID 195: both ears  
[2025-07-22 15:07:33] local.INFO: Successfully migrated service ID 41 → Laravel ID 196: Dressing -simple  
[2025-07-22 15:07:33] local.INFO: Successfully migrated service ID 42 → Laravel ID 197: Dressing- complex  
[2025-07-22 15:07:33] local.INFO: Successfully migrated service ID 43 → Laravel ID 198: Suture/Clip removal  
[2025-07-22 15:07:33] local.INFO: Successfully migrated service ID 44 → Laravel ID 199: own medication  
[2025-07-22 15:07:33] local.INFO: Successfully migrated service ID 45 → Laravel ID 200: B12  
[2025-07-22 15:07:33] local.INFO: Successfully migrated service ID 46 → Laravel ID 201: biotin  
[2025-07-22 15:07:34] local.INFO: Successfully migrated service ID 47 → Laravel ID 202: own form  
[2025-07-22 15:07:34] local.INFO: Successfully migrated service ID 249 → Laravel ID 203: IV administration only  
[2025-07-22 15:07:34] local.INFO: Successfully migrated service ID 357 → Laravel ID 204: Video Follow up - 7.5mg/10mg  
[2025-07-22 15:07:34] local.INFO: Successfully migrated service ID 358 → Laravel ID 205: Video Follow up - 12.5mg/15mg  
[2025-07-22 15:07:34] local.INFO: Successfully migrated service ID 359 → Laravel ID 206: In-Clinic Follow up - 2.5mg/5mg  
[2025-07-22 15:07:34] local.INFO: Successfully migrated service ID 360 → Laravel ID 207: In-Clinic Follow up - 5mg/7.5mg  
[2025-07-22 15:07:34] local.INFO: Successfully migrated service ID 361 → Laravel ID 208: In-Clinic Follow up - 12.5mg/15mg  
[2025-07-22 15:07:34] local.INFO: Successfully migrated service ID 400 → Laravel ID 209: Video Follow up - 2.5mg/5mg  
[2025-07-22 15:07:34] local.INFO: Successfully migrated service ID 426 → Laravel ID 210: Video Follow up - 2.5mg/5mg  
[2025-07-22 15:07:34] local.INFO: Clinic 5 completed: 23 processed, 0 skipped, 0 errors  
[2025-07-22 15:07:34] local.INFO: Starting services migration for clinic 4  
[2025-07-22 15:07:34] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:07:40] local.INFO: No services found for clinic 4  
[2025-07-22 15:07:40] local.INFO: Starting services migration for clinic 2  
[2025-07-22 15:07:40] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:07:44] local.INFO: No services found for clinic 2  
[2025-07-22 15:07:44] local.INFO: Starting services migration for clinic 1  
[2025-07-22 15:07:44] local.INFO: Making KiviCare API request: laravel_get_clinic_services  
[2025-07-22 15:07:49] local.INFO: Found 3 services for clinic 1  
[2025-07-22 15:07:49] local.INFO: Successfully migrated service ID 17 → Laravel ID 6: GP Services  
[2025-07-22 15:07:49] local.INFO: Successfully migrated service ID 60 → Laravel ID 7: GP Consultation (Virtual) Test  
[2025-07-22 15:07:49] local.INFO: Successfully migrated service ID 215 → Laravel ID 8: Same Day GP  
[2025-07-22 15:07:49] local.INFO: Clinic 1 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 15:07:49] local.INFO: Migration completed - Processed: 202, Skipped: 0, Errors: 0  
[2025-07-22 15:07:49] local.INFO: Migration command completed successfully  
