[2025-07-22 13:28:09] local.ERROR: Failed to migrate service ID 320: SQLSTATE[HY000]: General error: 1364 Field 'provider_id' doesn't have a default value (Connection: mysql, SQL: insert into `services` (`wp_service_id`, `name`, `description`, `type`, `price`, `duration`, `active`, `clinic_id`, `category_id`, `updated_at`, `created_at`) values (320, Medication, , gp, 200, 30, 1, 40, 12, 2025-07-22 13:28:09, 2025-07-22 13:28:09)) {"id":"320","name":"Medication","price":"200","status":"1","created_at":"2025-04-30 10:42:06","clinic_id":"40","doctor_id":"702","category_id":"4","type":"gp","updated_at":null,"charges":"200","duration":"30","description":null,"level":"clinic"} 
[2025-07-22 13:28:09] local.ERROR: Failed to migrate service ID 321: SQLSTATE[HY000]: General error: 1364 Field 'provider_id' doesn't have a default value (Connection: mysql, SQL: insert into `services` (`wp_service_id`, `name`, `description`, `type`, `price`, `duration`, `active`, `clinic_id`, `category_id`, `updated_at`, `created_at`) values (321, Blood Tests, , acupuncture, 150, 45, 1, 40, 1, 2025-07-22 13:28:09, 2025-07-22 13:28:09)) {"id":"321","name":"Blood Tests","price":"150","status":"1","created_at":"2025-04-30 10:44:19","clinic_id":"40","doctor_id":"702","category_id":"39","type":"acupuncture","updated_at":null,"charges":"150","duration":"45","description":null,"level":"clinic"} 
[2025-07-22 13:28:09] local.ERROR: Failed to migrate service ID 322: SQLSTATE[HY000]: General error: 1364 Field 'provider_id' doesn't have a default value (Connection: mysql, SQL: insert into `services` (`wp_service_id`, `name`, `description`, `type`, `price`, `duration`, `active`, `clinic_id`, `category_id`, `updated_at`, `created_at`) values (322, Virtual consultation, , gp, 120, 20, 1, 40, 12, 2025-07-22 13:28:09, 2025-07-22 13:28:09)) {"id":"322","name":"Virtual consultation","price":"120","status":"1","created_at":"2025-04-30 10:45:36","clinic_id":"40","doctor_id":"702","category_id":"4","type":"gp","updated_at":null,"charges":"120","duration":"20","description":null,"level":"clinic"} 
[2025-07-22 13:29:35] local.ERROR: Failed to migrate service ID 320: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_user_id' in 'where clause' (Connection: mysql, SQL: select * from `providers` where `wp_user_id` = 702 limit 1) {"id":"320","name":"Medication","price":"200","status":"1","created_at":"2025-04-30 10:42:06","clinic_id":"40","doctor_id":"702","category_id":"4","type":"gp","updated_at":null,"charges":"200","duration":"30","description":null,"level":"clinic"} 
[2025-07-22 13:29:35] local.ERROR: Failed to migrate service ID 321: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_user_id' in 'where clause' (Connection: mysql, SQL: select * from `providers` where `wp_user_id` = 702 limit 1) {"id":"321","name":"Blood Tests","price":"150","status":"1","created_at":"2025-04-30 10:44:19","clinic_id":"40","doctor_id":"702","category_id":"39","type":"acupuncture","updated_at":null,"charges":"150","duration":"45","description":null,"level":"clinic"} 
[2025-07-22 13:29:35] local.ERROR: Failed to migrate service ID 322: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'wp_user_id' in 'where clause' (Connection: mysql, SQL: select * from `providers` where `wp_user_id` = 702 limit 1) {"id":"322","name":"Virtual consultation","price":"120","status":"1","created_at":"2025-04-30 10:45:36","clinic_id":"40","doctor_id":"702","category_id":"4","type":"gp","updated_at":null,"charges":"120","duration":"20","description":null,"level":"clinic"} 
[2025-07-22 13:30:11] local.ERROR: Failed to migrate service ID 320: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`services`, CONSTRAINT `services_clinic_id_foreign` FOREIGN KEY (`clinic_id`) REFERENCES `clinics` (`id`) ON DELETE SET NULL) (Connection: mysql, SQL: insert into `services` (`wp_service_id`, `name`, `description`, `type`, `price`, `duration`, `active`, `clinic_id`, `provider_id`, `category_id`, `updated_at`, `created_at`) values (320, Medication, , gp, 200, 30, 1, 40, ?, 12, 2025-07-22 13:30:11, 2025-07-22 13:30:11)) {"id":"320","name":"Medication","price":"200","status":"1","created_at":"2025-04-30 10:42:06","clinic_id":"40","doctor_id":"702","category_id":"4","type":"gp","updated_at":null,"charges":"200","duration":"30","description":null,"level":"clinic"} 
[2025-07-22 13:30:11] local.ERROR: Failed to migrate service ID 321: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`services`, CONSTRAINT `services_clinic_id_foreign` FOREIGN KEY (`clinic_id`) REFERENCES `clinics` (`id`) ON DELETE SET NULL) (Connection: mysql, SQL: insert into `services` (`wp_service_id`, `name`, `description`, `type`, `price`, `duration`, `active`, `clinic_id`, `provider_id`, `category_id`, `updated_at`, `created_at`) values (321, Blood Tests, , acupuncture, 150, 45, 1, 40, ?, 1, 2025-07-22 13:30:11, 2025-07-22 13:30:11)) {"id":"321","name":"Blood Tests","price":"150","status":"1","created_at":"2025-04-30 10:44:19","clinic_id":"40","doctor_id":"702","category_id":"39","type":"acupuncture","updated_at":null,"charges":"150","duration":"45","description":null,"level":"clinic"} 
[2025-07-22 13:30:11] local.ERROR: Failed to migrate service ID 322: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`services`, CONSTRAINT `services_clinic_id_foreign` FOREIGN KEY (`clinic_id`) REFERENCES `clinics` (`id`) ON DELETE SET NULL) (Connection: mysql, SQL: insert into `services` (`wp_service_id`, `name`, `description`, `type`, `price`, `duration`, `active`, `clinic_id`, `provider_id`, `category_id`, `updated_at`, `created_at`) values (322, Virtual consultation, , gp, 120, 20, 1, 40, ?, 12, 2025-07-22 13:30:11, 2025-07-22 13:30:11)) {"id":"322","name":"Virtual consultation","price":"120","status":"1","created_at":"2025-04-30 10:45:36","clinic_id":"40","doctor_id":"702","category_id":"4","type":"gp","updated_at":null,"charges":"120","duration":"20","description":null,"level":"clinic"} 
[2025-07-22 15:01:35] local.ERROR: Failed to migrate service ID 320: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`services`, CONSTRAINT `services_clinic_id_foreign` FOREIGN KEY (`clinic_id`) REFERENCES `clinics` (`id`) ON DELETE SET NULL) (Connection: mysql, SQL: insert into `services` (`wp_service_id`, `name`, `description`, `type`, `price`, `duration`, `active`, `clinic_id`, `provider_id`, `category_id`, `updated_at`, `created_at`) values (320, Medication, , gp, 200, 30, 1, 40, 95, 12, 2025-07-22 15:01:35, 2025-07-22 15:01:35)) {"id":"320","name":"Medication","price":"200","status":"1","created_at":"2025-04-30 10:42:06","clinic_id":"40","doctor_id":"702","category_id":"4","type":"gp","updated_at":null,"charges":"200","duration":"30","description":null,"level":"clinic"} 
[2025-07-22 15:01:35] local.ERROR: Failed to migrate service ID 321: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`services`, CONSTRAINT `services_clinic_id_foreign` FOREIGN KEY (`clinic_id`) REFERENCES `clinics` (`id`) ON DELETE SET NULL) (Connection: mysql, SQL: insert into `services` (`wp_service_id`, `name`, `description`, `type`, `price`, `duration`, `active`, `clinic_id`, `provider_id`, `category_id`, `updated_at`, `created_at`) values (321, Blood Tests, , acupuncture, 150, 45, 1, 40, 95, 1, 2025-07-22 15:01:35, 2025-07-22 15:01:35)) {"id":"321","name":"Blood Tests","price":"150","status":"1","created_at":"2025-04-30 10:44:19","clinic_id":"40","doctor_id":"702","category_id":"39","type":"acupuncture","updated_at":null,"charges":"150","duration":"45","description":null,"level":"clinic"} 
[2025-07-22 15:01:35] local.ERROR: Failed to migrate service ID 322: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`medroid_app_db`.`services`, CONSTRAINT `services_clinic_id_foreign` FOREIGN KEY (`clinic_id`) REFERENCES `clinics` (`id`) ON DELETE SET NULL) (Connection: mysql, SQL: insert into `services` (`wp_service_id`, `name`, `description`, `type`, `price`, `duration`, `active`, `clinic_id`, `provider_id`, `category_id`, `updated_at`, `created_at`) values (322, Virtual consultation, , gp, 120, 20, 1, 40, 95, 12, 2025-07-22 15:01:35, 2025-07-22 15:01:35)) {"id":"322","name":"Virtual consultation","price":"120","status":"1","created_at":"2025-04-30 10:45:36","clinic_id":"40","doctor_id":"702","category_id":"4","type":"gp","updated_at":null,"charges":"120","duration":"20","description":null,"level":"clinic"} 
