<?php

namespace App\Console\Commands\Migration;

use App\Models\Service;
use App\Models\Category;
use App\Services\Migration\DataTransformer;
use Exception;
use Illuminate\Support\Facades\Log;

/**
 * Migrate Services Command
 * 
 * Simple focused migration for services only
 */
class MigrateServices extends BaseMigrationCommand
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'migratewp:services
                            {--clinic= : Specific clinic ID, comma-separated IDs, or "all" for all clinics}
                            {--dry-run : Preview what would be migrated without executing}
                            {--force : Skip confirmation prompts}';

    /**
     * The console command description.
     */
    protected $description = 'Migrate services from WordPress to Laravel';

    /**
     * Data transformer instance
     */
    protected $transformer;

    /**
     * Log channels
     */
    protected $logChannel;
    protected $errorLogChannel;

    public function __construct()
    {
        parent::__construct();
        $this->transformer = new DataTransformer();
        $this->setupLogging();
    }

    /**
     * Setup dedicated logging for services migration
     */
    protected function setupLogging()
    {
        $date = now()->format('Y_m_d');
        $logPath = storage_path("logs/migration/services");
        
        // Create directory if it doesn't exist
        if (!file_exists($logPath)) {
            mkdir($logPath, 0755, true);
        }

        // Configure log channels
        config([
            'logging.channels.services_migration' => [
                'driver' => 'single',
                'path' => $logPath . "/services_{$date}.log",
                'level' => 'info',
            ],
            'logging.channels.services_errors' => [
                'driver' => 'single',
                'path' => $logPath . "/services_errors_{$date}.log",
                'level' => 'error',
            ]
        ]);

        $this->logChannel = 'services_migration';
        $this->errorLogChannel = 'services_errors';
    }

    /**
     * Execute the command
     */
    protected function executeCommand()
    {
        $this->info("=== MIGRATING SERVICES ===");
        $this->logInfo("Starting services migration");
        
        if ($this->isDryRun()) {
            $this->warn("DRY RUN MODE - Previewing migration without making changes");
            $this->logInfo("Running in DRY RUN mode");
        }

        $clinicOption = $this->option('clinic') ?? 'all';
        $clinicIds = $this->parseClinicOption($clinicOption);

        $totalProcessed = 0;
        $totalSkipped = 0;
        $totalErrors = 0;
        $failedClinics = [];

        foreach ($clinicIds as $clinicId) {
            $this->info("\n--- Migrating services for clinic ID: {$clinicId} ---");
            $this->logInfo("Starting services migration for clinic {$clinicId}");
            
            try {
                // 1. Make API call - get services for this clinic
                $response = $this->makeApiRequest('laravel_get_clinic_services', ['clinic_id' => $clinicId]);
                $wpServices = $response['data'] ?? [];

                if (empty($wpServices)) {
                    $this->info("No services found for clinic {$clinicId}");
                    $this->logInfo("No services found for clinic {$clinicId}");
                    continue;
                }

                $this->info("Found " . count($wpServices) . " services for clinic {$clinicId}");
                $this->logInfo("Found " . count($wpServices) . " services for clinic {$clinicId}");

                $processed = 0;
                $skipped = 0;
                $errors = 0;

                // 2. Process each service
                foreach ($wpServices as $wpService) {
                    try {
                        $serviceName = $wpService['name'] ?? 'Unknown Service';
                        $servicePrice = $wpService['price'] ?? 0;
                        $categoryId = $wpService['category_id'] ?? null;

                        if ($this->isDryRun()) {
                            $this->info("Would migrate service: {$serviceName} (Price: {$servicePrice}, Category ID: {$categoryId})");
                            $this->logInfo("DRY RUN: Would migrate service ID {$wpService['id']}: {$serviceName} (Price: {$servicePrice}, Category ID: {$categoryId})");
                            $processed++;
                            continue;
                        }

                        // Handle category mapping if category_id exists
                        $laravelCategoryId = null;
                        if ($categoryId) {
                            $laravelCategoryId = $this->ensureCategoryExists($categoryId, $wpService['type'] ?? 'general');
                        }

                        // Handle category creation if needed
                        $laravelCategoryId = null;
                        if (!empty($wpService['category_id'])) {
                            $laravelCategoryId = $this->ensureCategoryExists($wpService['category_id'], $wpService['type'] ?? 'general');
                        }

                        // Map WordPress clinic ID to Laravel clinic ID
                        $laravelClinicId = $this->mapWordPressClinicToLaravel($clinicId);
                        if (!$laravelClinicId) {
                            $this->error("✗ Laravel clinic not found for WordPress clinic ID {$clinicId}");
                            $this->logError("Laravel clinic not found for WordPress clinic ID {$clinicId}");

                            // Debug: Show what clinics exist
                            $existingClinics = \App\Models\Clinic::select('id', 'wp_clinic_id', 'name')->get();
                            $this->info("Available Laravel clinics:");
                            foreach ($existingClinics as $clinic) {
                                $this->info("- Laravel ID: {$clinic->id}, WP ID: {$clinic->wp_clinic_id}, Name: {$clinic->name}");
                            }

                            $errors++;
                            continue;
                        }

                        $this->info("✓ Clinic mapped: WP clinic ID {$clinicId} → Laravel clinic ID {$laravelClinicId}");

                        // Transform WordPress service data to Laravel format
                        $laravelData = $this->transformer->transformService($wpService);
                        $laravelData['wp_service_id'] = $wpService['id'];
                        $laravelData['clinic_id'] = $laravelClinicId;
                        $laravelData['category_id'] = $laravelCategoryId;

                        // Log provider mapping status
                        $wpDoctorId = $wpService['doctor_id'] ?? null;
                        if ($wpDoctorId && !$laravelData['provider_id']) {
                            $this->info("⚠️  Provider not found for WordPress doctor ID {$wpDoctorId} (service: {$serviceName})");
                            $this->logInfo("Provider mapping failed: WP doctor ID {$wpDoctorId} not found in Laravel users/providers");
                        } elseif ($laravelData['provider_id']) {
                            $this->info("✓ Provider mapped: WP doctor ID {$wpDoctorId} → Laravel provider ID {$laravelData['provider_id']}");
                        }
                        $laravelData['category_id'] = $laravelCategoryId;

                        // Create or update service
                        $service = Service::updateOrCreate(
                            ['wp_service_id' => $wpService['id']],
                            $laravelData
                        );

                        $this->info("✓ Migrated service: {$service->name} (Laravel ID: {$service->id}, Price: {$service->price})");
                        $this->logInfo("Successfully migrated service ID {$wpService['id']} → Laravel ID {$service->id}: {$service->name}");
                        $processed++;

                    } catch (Exception $e) {
                        $this->error("✗ Failed to migrate service ID {$wpService['id']}: " . $e->getMessage());
                        $this->logError("Failed to migrate service ID {$wpService['id']}: " . $e->getMessage(), $wpService);
                        $errors++;
                    }
                }

                $this->info("Clinic {$clinicId} completed: {$processed} processed, {$skipped} skipped, {$errors} errors");
                $this->logInfo("Clinic {$clinicId} completed: {$processed} processed, {$skipped} skipped, {$errors} errors");

                $totalProcessed += $processed;
                $totalSkipped += $skipped;
                $totalErrors += $errors;

            } catch (Exception $e) {
                $this->error("❌ Failed to process clinic {$clinicId}: " . $e->getMessage());
                $this->logError("Failed to process clinic {$clinicId}: " . $e->getMessage());
                $failedClinics[] = $clinicId;
                continue;
            }
        }

        // 3. Generate summary
        $this->generateSummary($totalProcessed, $totalSkipped, $totalErrors, $failedClinics);

        return 0;
    }

    /**
     * Generate migration summary
     */
    protected function generateSummary($processed, $skipped, $errors, $failedClinics)
    {
        $this->info("\n" . str_repeat("=", 60));
        $this->info("📊 SERVICES MIGRATION SUMMARY");
        $this->info(str_repeat("=", 60));
        $this->info("✅ Processed: {$processed}");
        $this->info("⏭️  Skipped: {$skipped}");
        $this->info("❌ Errors: {$errors}");
        
        if (!empty($failedClinics)) {
            $this->error("🚨 Failed Clinics: " . implode(', ', $failedClinics));
        }
        
        if ($errors > 0 || !empty($failedClinics)) {
            $this->error("\n⚠️  Some services failed to migrate. Check error logs for details.");
        } else {
            $this->info("\n🎉 All services migrated successfully!");
        }
        
        $this->info("📁 Logs saved to: storage/logs/migration/services/");
        $this->info(str_repeat("=", 60));

        // Log summary
        $this->logInfo("Migration completed - Processed: {$processed}, Skipped: {$skipped}, Errors: {$errors}");
        if (!empty($failedClinics)) {
            $this->logError("Failed clinics: " . implode(', ', $failedClinics));
        }
    }

    /**
     * Log info message
     */
    protected function logInfo($message, $context = [])
    {
        Log::channel($this->logChannel)->info($message, $context);
    }

    /**
     * Log error message
     */
    protected function logError($message, $context = [])
    {
        Log::channel($this->errorLogChannel)->error($message, $context);
    }

    /**
     * Ensure category exists, create if not found
     */
    protected function ensureCategoryExists($wpCategoryId, $serviceType)
    {
        // First try to find existing category by wp_category_id
        $category = Category::where('wp_category_id', $wpCategoryId)->first();

        if ($category) {
            return $category->id;
        }

        // If not found, create a new category based on service type
        $categoryName = ucwords(str_replace('_', ' ', $serviceType));

        try {
            $category = Category::create([
                'wp_category_id' => $wpCategoryId,
                'name' => $categoryName,
                'description' => "Category for {$categoryName} services",
                'is_active' => true,
                'clinic_id' => null, // Global category
            ]);

            $this->info("✓ Created category: {$category->name} (WP ID: {$wpCategoryId})");
            $this->logInfo("Created category: {$category->name} (WP ID: {$wpCategoryId})");

            return $category->id;
        } catch (Exception $e) {
            $this->error("✗ Failed to create category for WP ID {$wpCategoryId}: " . $e->getMessage());
            $this->logError("Failed to create category for WP ID {$wpCategoryId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Map WordPress clinic ID to Laravel clinic ID
     */
    protected function mapWordPressClinicToLaravel($wpClinicId)
    {
        // Find Laravel clinic by wp_clinic_id
        $clinic = \App\Models\Clinic::where('wp_clinic_id', $wpClinicId)->first();

        if (!$clinic) {
            return null;
        }

        return $clinic->id;
    }
}
