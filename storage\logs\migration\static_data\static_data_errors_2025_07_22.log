[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 72: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (72, aesthetics, Aesthetics, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"72","name":"Aesthetics","value":"aesthetics","type":"specialization","status":"1","created_at":"2024-12-19 21:48:28"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 4: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (4, allergy_and_immunology, Allergy And Immunology, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"4","name":"Allergy And Immunology","value":"allergy_and_immunology","type":"specialization","status":"1","created_at":"2024-11-04 11:17:52"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 153: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (153, cardiac_physiologist, Cardiac Physiologist, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"153","name":"Cardiac Physiologist","value":"cardiac_physiologist","type":"specialization","status":"1","created_at":"2025-06-23 14:04:43"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 123: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (123, cardiology, Cardiology, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"123","name":"Cardiology","value":"cardiology","type":"specialization","status":"1","created_at":"2025-03-03 06:38:51"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 1: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (1, dermatology, Dermatology, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"1","name":"Dermatology","value":"dermatology","type":"specialization","status":"1","created_at":"2024-11-04 11:17:52"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 117: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (117, diagnostics, Diagnostics, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"117","name":"Diagnostics","value":"diagnostics","type":"specialization","status":"1","created_at":"2025-02-18 14:52:17"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 2: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (2, family_medicine, Family Medicine, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"2","name":"Family Medicine","value":"family_medicine","type":"specialization","status":"1","created_at":"2024-11-04 11:17:52"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 18: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (18, general_practice, General Practice, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"18","name":"General Practice","value":"general_practice","type":"specialization","status":"1","created_at":"2024-11-25 10:42:13"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 139: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (139, general_surgery, General Surgery, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"139","name":"General Surgery","value":"general_surgery","type":"specialization","status":"1","created_at":"2025-04-16 13:37:45"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 71: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (71, gynaecology, Gynaecology, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"71","name":"Gynaecology","value":"gynaecology","type":"specialization","status":"1","created_at":"2024-12-19 21:48:20"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 146: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (146, joint_injections, Joint Injections, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"146","name":"Joint Injections","value":"joint_injections","type":"specialization","status":"1","created_at":"2025-05-04 10:46:43"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 3: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (3, neurology, Neurology, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"3","name":"Neurology","value":"neurology","type":"specialization","status":"1","created_at":"2024-11-04 11:17:52"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 19: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (19, nurse_specialist, Nurse Specialist, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"19","name":"Nurse Specialist","value":"nurse_specialist","type":"specialization","status":"1","created_at":"2024-11-25 11:16:33"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 79: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (79, obstetrics, Obstetrics, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"79","name":"Obstetrics","value":"obstetrics","type":"specialization","status":"1","created_at":"2025-01-10 12:12:12"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 70: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (70, orthopaedics, Orthopaedics, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"70","name":"Orthopaedics","value":"orthopaedics","type":"specialization","status":"1","created_at":"2024-12-19 21:48:12"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 73: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (73, paediatrics, Paediatrics, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"73","name":"Paediatrics","value":"paediatrics","type":"specialization","status":"1","created_at":"2024-12-19 21:48:37"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 136: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (136, phlebotomy, Phlebotomy, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"136","name":"Phlebotomy","value":"phlebotomy","type":"specialization","status":"1","created_at":"2025-03-31 12:07:33"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 20: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (20, psychiatry, Psychiatry, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"20","name":"Psychiatry","value":"psychiatry","type":"specialization","status":"1","created_at":"2024-11-25 11:46:49"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 128: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (128, respiratory, Respiratory, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"128","name":"Respiratory","value":"respiratory","type":"specialization","status":"1","created_at":"2025-03-06 17:05:00"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 135: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (135, rheumatology, Rheumatology, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"135","name":"Rheumatology","value":"rheumatology","type":"specialization","status":"1","created_at":"2025-03-25 15:30:00"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 83: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (83, urgent_care, Urgent Care, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"83","name":"Urgent Care","value":"urgent_care","type":"specialization","status":"1","created_at":"2025-01-22 12:29:01"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 131: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (131, urologist, Urologist, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"131","name":"Urologist","value":"urologist","type":"specialization","status":"1","created_at":"2025-03-19 12:01:50"} 
[2025-07-22 15:30:54] local.ERROR: Failed to migrate specialization ID 88: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'specialty_id' in 'field list' (Connection: mysql, SQL: insert into `specializations` (`wp_specialization_id`, `name`, `label`, `description`, `specialty_id`, `is_active`, `sort_order`, `updated_at`, `created_at`) values (88, weight_loss, Weight Loss, , ?, 1, 0, 2025-07-22 15:30:54, 2025-07-22 15:30:54)) {"id":"88","name":"Weight Loss","value":"weight_loss","type":"specialization","status":"1","created_at":"2025-01-26 21:16:22"} 
