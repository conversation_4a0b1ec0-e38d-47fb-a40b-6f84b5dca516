[2025-07-22 10:57:02] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 10:57:02] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 10:57:07] local.INFO: WordPress API connection validated  
[2025-07-22 10:57:07] local.INFO: Database connection validated  
[2025-07-22 10:57:07] local.INFO: Starting users migration  
[2025-07-22 10:57:07] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 10:57:12] local.INFO: Starting users migration for clinic 40  
[2025-07-22 10:57:12] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:57:18] local.INFO: Found 5 users for clinic 40  
[2025-07-22 10:57:18] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 2, Role: provider  
[2025-07-22 10:57:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 3, Role: patient  
[2025-07-22 10:57:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 4, Role: patient  
[2025-07-22 10:57:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 5, Role: staff  
[2025-07-22 10:57:19] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 6, Role: clinic_admin  
[2025-07-22 10:57:19] local.INFO: Clinic 40 completed: 5 processed, 0 skipped, 0 errors  
[2025-07-22 10:57:19] local.INFO: Starting users migration for clinic 27  
[2025-07-22 10:57:19] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:57:24] local.INFO: Found 1 users for clinic 27  
[2025-07-22 10:57:24] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 7, Role: clinic_admin  
[2025-07-22 10:57:24] local.INFO: Clinic 27 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 10:57:24] local.INFO: Starting users migration for clinic 26  
[2025-07-22 10:57:24] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:57:29] local.INFO: Found 1 users for clinic 26  
[2025-07-22 10:57:30] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 8, Role: clinic_admin  
[2025-07-22 10:57:30] local.INFO: Clinic 26 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 10:57:30] local.INFO: Starting users migration for clinic 22  
[2025-07-22 10:57:30] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:57:35] local.INFO: Found 1 users for clinic 22  
[2025-07-22 10:57:35] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 9, Role: clinic_admin  
[2025-07-22 10:57:35] local.INFO: Clinic 22 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 10:57:35] local.INFO: Starting users migration for clinic 16  
[2025-07-22 10:57:35] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:57:41] local.INFO: Found 7 users for clinic 16  
[2025-07-22 10:57:41] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 10, Role: provider  
[2025-07-22 10:57:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 11, Role: patient  
[2025-07-22 10:57:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 12, Role: patient  
[2025-07-22 10:57:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 13, Role: patient  
[2025-07-22 10:57:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 14, Role: patient  
[2025-07-22 10:57:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 15, Role: patient  
[2025-07-22 10:57:42] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 16, Role: clinic_admin  
[2025-07-22 10:57:42] local.INFO: Clinic 16 completed: 7 processed, 0 skipped, 0 errors  
[2025-07-22 10:57:42] local.INFO: Starting users migration for clinic 15  
[2025-07-22 10:57:42] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:57:48] local.INFO: Found 5 users for clinic 15  
[2025-07-22 10:57:48] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 17, Role: provider  
[2025-07-22 10:57:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 18, Role: patient  
[2025-07-22 10:57:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 19, Role: patient  
[2025-07-22 10:57:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 20, Role: patient  
[2025-07-22 10:57:49] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 21, Role: clinic_admin  
[2025-07-22 10:57:49] local.INFO: Clinic 15 completed: 5 processed, 0 skipped, 0 errors  
[2025-07-22 10:57:49] local.INFO: Starting users migration for clinic 14  
[2025-07-22 10:57:49] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 10:57:54] local.INFO: Found 524 users for clinic 14  
[2025-07-22 10:57:54] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 22, Role: provider  
[2025-07-22 10:57:54] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 23, Role: provider  
[2025-07-22 10:57:54] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 24, Role: provider  
[2025-07-22 10:57:55] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 25, Role: provider  
[2025-07-22 10:57:55] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 26, Role: provider  
[2025-07-22 10:57:55] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 27, Role: provider  
[2025-07-22 10:57:55] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 28, Role: provider  
[2025-07-22 10:57:56] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 29, Role: provider  
[2025-07-22 10:57:56] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 30, Role: provider  
[2025-07-22 10:57:56] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 31, Role: provider  
[2025-07-22 10:57:56] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 32, Role: provider  
[2025-07-22 10:57:56] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 33, Role: provider  
[2025-07-22 10:57:57] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 34, Role: provider  
[2025-07-22 10:57:57] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 35, Role: provider  
[2025-07-22 10:57:57] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 36, Role: provider  
[2025-07-22 10:57:57] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 37, Role: provider  
[2025-07-22 10:57:57] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 38, Role: provider  
[2025-07-22 10:57:58] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 39, Role: provider  
[2025-07-22 10:57:58] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 40, Role: provider  
[2025-07-22 10:57:58] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 41, Role: provider  
[2025-07-22 10:57:58] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 10:57:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 42, Role: provider  
[2025-07-22 10:57:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 43, Role: patient  
[2025-07-22 10:57:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 44, Role: patient  
[2025-07-22 10:57:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 45, Role: patient  
[2025-07-22 10:57:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 46, Role: patient  
[2025-07-22 10:57:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 47, Role: patient  
[2025-07-22 10:57:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 48, Role: patient  
[2025-07-22 10:58:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 49, Role: patient  
[2025-07-22 10:58:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 50, Role: patient  
[2025-07-22 10:58:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 51, Role: patient  
[2025-07-22 10:58:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 52, Role: patient  
[2025-07-22 10:58:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 53, Role: patient  
[2025-07-22 10:58:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 54, Role: patient  
[2025-07-22 10:58:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 55, Role: patient  
[2025-07-22 10:58:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 56, Role: patient  
[2025-07-22 10:58:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 57, Role: patient  
[2025-07-22 10:58:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 58, Role: patient  
[2025-07-22 10:58:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 59, Role: patient  
[2025-07-22 10:58:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 60, Role: patient  
[2025-07-22 10:58:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 61, Role: patient  
[2025-07-22 10:58:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 62, Role: patient  
[2025-07-22 10:58:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 63, Role: patient  
[2025-07-22 10:58:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 64, Role: patient  
[2025-07-22 10:58:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 65, Role: patient  
[2025-07-22 10:58:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 66, Role: patient  
[2025-07-22 10:58:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 67, Role: patient  
[2025-07-22 10:58:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 68, Role: patient  
[2025-07-22 10:58:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 69, Role: patient  
[2025-07-22 10:58:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 70, Role: patient  
[2025-07-22 10:58:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 71, Role: patient  
[2025-07-22 10:58:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 72, Role: patient  
[2025-07-22 10:58:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 73, Role: patient  
[2025-07-22 10:58:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 74, Role: patient  
[2025-07-22 10:58:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 75, Role: patient  
[2025-07-22 10:58:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 76, Role: patient  
[2025-07-22 10:58:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 77, Role: patient  
[2025-07-22 10:58:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 78, Role: patient  
[2025-07-22 10:58:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 79, Role: patient  
[2025-07-22 10:58:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 80, Role: patient  
[2025-07-22 10:58:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 81, Role: patient  
[2025-07-22 10:58:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 82, Role: patient  
[2025-07-22 10:58:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 83, Role: patient  
[2025-07-22 10:58:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 84, Role: patient  
[2025-07-22 10:58:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 85, Role: patient  
[2025-07-22 10:58:07] local.INFO: Successfully migrated user majetyanupam+_878^<EMAIL> → Laravel ID 86, Role: patient  
[2025-07-22 10:58:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 87, Role: patient  
[2025-07-22 10:58:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 88, Role: patient  
[2025-07-22 10:58:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 89, Role: patient  
[2025-07-22 10:58:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 90, Role: patient  
[2025-07-22 10:58:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 91, Role: patient  
[2025-07-22 10:58:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 92, Role: patient  
[2025-07-22 10:58:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 93, Role: patient  
[2025-07-22 10:58:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 94, Role: patient  
[2025-07-22 10:58:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 95, Role: patient  
[2025-07-22 10:58:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 96, Role: patient  
[2025-07-22 10:58:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 97, Role: patient  
[2025-07-22 10:58:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 98, Role: patient  
[2025-07-22 10:58:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 99, Role: patient  
[2025-07-22 10:58:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 100, Role: patient  
[2025-07-22 10:58:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 101, Role: patient  
[2025-07-22 10:58:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 102, Role: patient  
[2025-07-22 10:58:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 103, Role: patient  
[2025-07-22 10:58:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 104, Role: patient  
[2025-07-22 10:58:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 105, Role: patient  
[2025-07-22 10:58:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 106, Role: patient  
[2025-07-22 10:58:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 107, Role: patient  
[2025-07-22 10:58:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 108, Role: patient  
[2025-07-22 10:58:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 109, Role: patient  
[2025-07-22 10:58:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 110, Role: patient  
[2025-07-22 10:58:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 111, Role: patient  
[2025-07-22 10:58:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 112, Role: patient  
[2025-07-22 10:58:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 113, Role: patient  
[2025-07-22 10:58:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 114, Role: patient  
[2025-07-22 10:58:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 115, Role: patient  
[2025-07-22 10:58:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 116, Role: patient  
[2025-07-22 10:58:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 117, Role: patient  
[2025-07-22 10:58:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 118, Role: patient  
[2025-07-22 10:58:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 119, Role: patient  
[2025-07-22 10:58:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 120, Role: patient  
[2025-07-22 10:58:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 121, Role: patient  
[2025-07-22 10:58:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 122, Role: patient  
[2025-07-22 10:58:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 123, Role: patient  
[2025-07-22 10:58:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 124, Role: patient  
[2025-07-22 10:58:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 125, Role: patient  
[2025-07-22 10:58:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 126, Role: patient  
[2025-07-22 10:58:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 127, Role: patient  
[2025-07-22 10:58:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 128, Role: patient  
[2025-07-22 10:58:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 129, Role: patient  
[2025-07-22 10:58:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 130, Role: patient  
[2025-07-22 10:58:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 131, Role: patient  
[2025-07-22 10:58:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 132, Role: patient  
[2025-07-22 10:58:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 133, Role: patient  
[2025-07-22 10:58:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 134, Role: patient  
[2025-07-22 10:58:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 135, Role: patient  
[2025-07-22 10:58:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 136, Role: patient  
[2025-07-22 10:58:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 137, Role: patient  
[2025-07-22 10:58:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 138, Role: patient  
[2025-07-22 10:58:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 139, Role: patient  
[2025-07-22 10:58:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 140, Role: patient  
[2025-07-22 10:58:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 141, Role: patient  
[2025-07-22 10:58:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 142, Role: patient  
[2025-07-22 10:58:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 143, Role: patient  
[2025-07-22 10:58:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 144, Role: patient  
[2025-07-22 10:58:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 145, Role: patient  
[2025-07-22 10:58:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 146, Role: patient  
[2025-07-22 10:58:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 147, Role: patient  
[2025-07-22 10:58:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 148, Role: patient  
[2025-07-22 10:58:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 149, Role: patient  
[2025-07-22 11:01:32] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 11:01:32] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 11:01:37] local.INFO: WordPress API connection validated  
[2025-07-22 11:01:37] local.INFO: Database connection validated  
[2025-07-22 11:01:37] local.INFO: Starting users migration  
[2025-07-22 11:01:37] local.INFO: Starting users migration for clinic 14  
[2025-07-22 11:01:37] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:01:42] local.INFO: Found 524 users for clinic 14  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: Skipped user majetyanupam+_878^<EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:01:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 151, Role: patient  
[2025-07-22 11:01:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 152, Role: patient  
[2025-07-22 11:01:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 153, Role: patient  
[2025-07-22 11:01:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 154, Role: patient  
[2025-07-22 11:01:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 155, Role: patient  
[2025-07-22 11:01:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 156, Role: patient  
[2025-07-22 11:01:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 157, Role: patient  
[2025-07-22 11:01:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 158, Role: patient  
[2025-07-22 11:01:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 159, Role: patient  
[2025-07-22 11:01:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 160, Role: patient  
[2025-07-22 11:01:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 161, Role: patient  
[2025-07-22 11:01:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 162, Role: patient  
[2025-07-22 11:01:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 163, Role: patient  
[2025-07-22 11:01:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 164, Role: patient  
[2025-07-22 11:01:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 165, Role: patient  
[2025-07-22 11:01:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 166, Role: patient  
[2025-07-22 11:01:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 167, Role: patient  
[2025-07-22 11:01:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 168, Role: patient  
[2025-07-22 11:01:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 169, Role: patient  
[2025-07-22 11:01:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 170, Role: patient  
[2025-07-22 11:01:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 171, Role: patient  
[2025-07-22 11:01:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 172, Role: patient  
[2025-07-22 11:01:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 173, Role: patient  
[2025-07-22 11:01:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 174, Role: patient  
[2025-07-22 11:01:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 175, Role: patient  
[2025-07-22 11:01:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 176, Role: patient  
[2025-07-22 11:01:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 177, Role: patient  
[2025-07-22 11:01:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 178, Role: patient  
[2025-07-22 11:01:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 179, Role: patient  
[2025-07-22 11:01:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 180, Role: patient  
[2025-07-22 11:01:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 181, Role: patient  
[2025-07-22 11:01:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 182, Role: patient  
[2025-07-22 11:01:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 183, Role: patient  
[2025-07-22 11:01:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 184, Role: patient  
[2025-07-22 11:01:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 185, Role: patient  
[2025-07-22 11:01:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 186, Role: patient  
[2025-07-22 11:01:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 187, Role: patient  
[2025-07-22 11:01:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 188, Role: patient  
[2025-07-22 11:01:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 189, Role: patient  
[2025-07-22 11:01:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 190, Role: patient  
[2025-07-22 11:01:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 191, Role: patient  
[2025-07-22 11:01:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 192, Role: patient  
[2025-07-22 11:01:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 193, Role: patient  
[2025-07-22 11:01:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 194, Role: patient  
[2025-07-22 11:01:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 195, Role: patient  
[2025-07-22 11:01:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 196, Role: patient  
[2025-07-22 11:01:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 197, Role: patient  
[2025-07-22 11:01:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 198, Role: patient  
[2025-07-22 11:01:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 199, Role: patient  
[2025-07-22 11:01:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 200, Role: patient  
[2025-07-22 11:01:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 201, Role: patient  
[2025-07-22 11:01:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 202, Role: patient  
[2025-07-22 11:01:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 203, Role: patient  
[2025-07-22 11:01:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 204, Role: patient  
[2025-07-22 11:01:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 205, Role: patient  
[2025-07-22 11:01:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 206, Role: patient  
[2025-07-22 11:01:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 207, Role: patient  
[2025-07-22 11:01:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 208, Role: patient  
[2025-07-22 11:01:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 209, Role: patient  
[2025-07-22 11:01:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 210, Role: patient  
[2025-07-22 11:01:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 211, Role: patient  
[2025-07-22 11:01:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 212, Role: patient  
[2025-07-22 11:01:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 213, Role: patient  
[2025-07-22 11:01:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 214, Role: patient  
[2025-07-22 11:01:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 215, Role: patient  
[2025-07-22 11:01:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 216, Role: patient  
[2025-07-22 11:01:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 217, Role: patient  
[2025-07-22 11:01:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 218, Role: patient  
[2025-07-22 11:01:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 219, Role: patient  
[2025-07-22 11:01:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 220, Role: patient  
[2025-07-22 11:01:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 221, Role: patient  
[2025-07-22 11:01:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 222, Role: patient  
[2025-07-22 11:01:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 223, Role: patient  
[2025-07-22 11:01:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 224, Role: patient  
[2025-07-22 11:01:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 225, Role: patient  
[2025-07-22 11:01:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 226, Role: patient  
[2025-07-22 11:01:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 227, Role: patient  
[2025-07-22 11:01:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 228, Role: patient  
[2025-07-22 11:01:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 229, Role: patient  
[2025-07-22 11:01:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 230, Role: patient  
[2025-07-22 11:01:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 231, Role: patient  
[2025-07-22 11:01:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 232, Role: patient  
[2025-07-22 11:02:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 233, Role: patient  
[2025-07-22 11:02:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 234, Role: patient  
[2025-07-22 11:02:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 235, Role: patient  
[2025-07-22 11:02:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 236, Role: patient  
[2025-07-22 11:02:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 237, Role: patient  
[2025-07-22 11:02:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 238, Role: patient  
[2025-07-22 11:02:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 239, Role: patient  
[2025-07-22 11:02:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 240, Role: patient  
[2025-07-22 11:02:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 241, Role: patient  
[2025-07-22 11:02:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 242, Role: patient  
[2025-07-22 11:02:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 243, Role: patient  
[2025-07-22 11:02:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 244, Role: patient  
[2025-07-22 11:02:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 245, Role: patient  
[2025-07-22 11:02:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 246, Role: patient  
[2025-07-22 11:02:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 247, Role: patient  
[2025-07-22 11:02:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 248, Role: patient  
[2025-07-22 11:02:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 249, Role: patient  
[2025-07-22 11:02:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 250, Role: patient  
[2025-07-22 11:02:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 251, Role: patient  
[2025-07-22 11:02:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 252, Role: patient  
[2025-07-22 11:02:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 253, Role: patient  
[2025-07-22 11:02:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 254, Role: patient  
[2025-07-22 11:02:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 255, Role: patient  
[2025-07-22 11:02:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 256, Role: patient  
[2025-07-22 11:02:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 257, Role: patient  
[2025-07-22 11:02:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 258, Role: patient  
[2025-07-22 11:02:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 259, Role: patient  
[2025-07-22 11:02:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 260, Role: patient  
[2025-07-22 11:02:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 261, Role: patient  
[2025-07-22 11:02:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 262, Role: patient  
[2025-07-22 11:02:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 263, Role: patient  
[2025-07-22 11:02:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 264, Role: patient  
[2025-07-22 11:02:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 265, Role: patient  
[2025-07-22 11:02:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 266, Role: patient  
[2025-07-22 11:02:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 267, Role: patient  
[2025-07-22 11:02:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 268, Role: patient  
[2025-07-22 11:02:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 269, Role: patient  
[2025-07-22 11:02:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 270, Role: patient  
[2025-07-22 11:02:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 271, Role: patient  
[2025-07-22 11:02:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 272, Role: patient  
[2025-07-22 11:02:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 273, Role: patient  
[2025-07-22 11:02:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 274, Role: patient  
[2025-07-22 11:02:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 275, Role: patient  
[2025-07-22 11:02:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 276, Role: patient  
[2025-07-22 11:02:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 277, Role: patient  
[2025-07-22 11:02:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 278, Role: patient  
[2025-07-22 11:02:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 279, Role: patient  
[2025-07-22 11:02:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 280, Role: patient  
[2025-07-22 11:02:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 281, Role: patient  
[2025-07-22 11:02:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 282, Role: patient  
[2025-07-22 11:02:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 283, Role: patient  
[2025-07-22 11:02:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 284, Role: patient  
[2025-07-22 11:02:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 285, Role: patient  
[2025-07-22 11:02:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 286, Role: patient  
[2025-07-22 11:02:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 287, Role: patient  
[2025-07-22 11:02:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 288, Role: patient  
[2025-07-22 11:02:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 289, Role: patient  
[2025-07-22 11:02:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 290, Role: patient  
[2025-07-22 11:02:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 291, Role: patient  
[2025-07-22 11:02:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 292, Role: patient  
[2025-07-22 11:02:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 293, Role: patient  
[2025-07-22 11:02:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 294, Role: patient  
[2025-07-22 11:02:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 295, Role: patient  
[2025-07-22 11:02:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 296, Role: patient  
[2025-07-22 11:02:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 297, Role: patient  
[2025-07-22 11:02:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 298, Role: patient  
[2025-07-22 11:02:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 299, Role: patient  
[2025-07-22 11:02:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 300, Role: patient  
[2025-07-22 11:02:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 301, Role: patient  
[2025-07-22 11:02:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 302, Role: patient  
[2025-07-22 11:02:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 303, Role: patient  
[2025-07-22 11:02:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 304, Role: patient  
[2025-07-22 11:02:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 305, Role: patient  
[2025-07-22 11:02:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 306, Role: patient  
[2025-07-22 11:02:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 307, Role: patient  
[2025-07-22 11:02:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 308, Role: patient  
[2025-07-22 11:02:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 309, Role: patient  
[2025-07-22 11:02:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 310, Role: patient  
[2025-07-22 11:02:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 311, Role: patient  
[2025-07-22 11:02:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 312, Role: patient  
[2025-07-22 11:02:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 313, Role: patient  
[2025-07-22 11:02:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 314, Role: patient  
[2025-07-22 11:02:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 315, Role: patient  
[2025-07-22 11:02:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 316, Role: patient  
[2025-07-22 11:02:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 317, Role: patient  
[2025-07-22 11:02:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 318, Role: patient  
[2025-07-22 11:02:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 319, Role: patient  
[2025-07-22 11:02:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 320, Role: patient  
[2025-07-22 11:02:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 321, Role: patient  
[2025-07-22 11:02:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 322, Role: patient  
[2025-07-22 11:02:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 323, Role: patient  
[2025-07-22 11:02:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 324, Role: patient  
[2025-07-22 11:02:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 325, Role: patient  
[2025-07-22 11:02:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 326, Role: patient  
[2025-07-22 11:02:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 327, Role: patient  
[2025-07-22 11:02:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 328, Role: patient  
[2025-07-22 11:02:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 329, Role: patient  
[2025-07-22 11:02:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 330, Role: patient  
[2025-07-22 11:02:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 331, Role: patient  
[2025-07-22 11:02:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 332, Role: patient  
[2025-07-22 11:02:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 333, Role: patient  
[2025-07-22 11:02:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 334, Role: patient  
[2025-07-22 11:02:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 335, Role: patient  
[2025-07-22 11:02:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 336, Role: patient  
[2025-07-22 11:02:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 337, Role: patient  
[2025-07-22 11:02:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 338, Role: patient  
[2025-07-22 11:02:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 339, Role: patient  
[2025-07-22 11:02:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 340, Role: patient  
[2025-07-22 11:02:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 341, Role: patient  
[2025-07-22 11:02:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 342, Role: patient  
[2025-07-22 11:02:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 343, Role: patient  
[2025-07-22 11:02:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 344, Role: patient  
[2025-07-22 11:02:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 345, Role: patient  
[2025-07-22 11:02:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 346, Role: patient  
[2025-07-22 11:02:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 347, Role: patient  
[2025-07-22 11:02:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 348, Role: patient  
[2025-07-22 11:02:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 349, Role: patient  
[2025-07-22 11:02:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 350, Role: patient  
[2025-07-22 11:02:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 351, Role: patient  
[2025-07-22 11:02:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 352, Role: patient  
[2025-07-22 11:02:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 353, Role: patient  
[2025-07-22 11:02:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 354, Role: patient  
[2025-07-22 11:02:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 355, Role: patient  
[2025-07-22 11:02:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 356, Role: patient  
[2025-07-22 11:02:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 357, Role: patient  
[2025-07-22 11:02:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 358, Role: patient  
[2025-07-22 11:02:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 359, Role: patient  
[2025-07-22 11:02:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 360, Role: patient  
[2025-07-22 11:02:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 361, Role: patient  
[2025-07-22 11:02:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 362, Role: patient  
[2025-07-22 11:02:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 363, Role: patient  
[2025-07-22 11:02:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 364, Role: patient  
[2025-07-22 11:02:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 365, Role: patient  
[2025-07-22 11:02:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 366, Role: patient  
[2025-07-22 11:02:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 367, Role: patient  
[2025-07-22 11:02:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 368, Role: patient  
[2025-07-22 11:02:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 369, Role: patient  
[2025-07-22 11:02:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 370, Role: patient  
[2025-07-22 11:02:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 371, Role: patient  
[2025-07-22 11:02:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 372, Role: patient  
[2025-07-22 11:02:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 373, Role: patient  
[2025-07-22 11:02:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 374, Role: patient  
[2025-07-22 11:02:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 375, Role: patient  
[2025-07-22 11:02:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 376, Role: patient  
[2025-07-22 11:02:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 377, Role: patient  
[2025-07-22 11:02:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 378, Role: patient  
[2025-07-22 11:02:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 379, Role: patient  
[2025-07-22 11:02:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 380, Role: patient  
[2025-07-22 11:02:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 381, Role: patient  
[2025-07-22 11:02:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 382, Role: patient  
[2025-07-22 11:02:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 383, Role: patient  
[2025-07-22 11:02:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 384, Role: patient  
[2025-07-22 11:02:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 385, Role: patient  
[2025-07-22 11:02:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 386, Role: patient  
[2025-07-22 11:02:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 387, Role: patient  
[2025-07-22 11:02:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 388, Role: patient  
[2025-07-22 11:02:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 389, Role: patient  
[2025-07-22 11:02:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 390, Role: patient  
[2025-07-22 11:02:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 391, Role: patient  
[2025-07-22 11:02:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 392, Role: patient  
[2025-07-22 11:02:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 393, Role: patient  
[2025-07-22 11:02:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 394, Role: patient  
[2025-07-22 11:02:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 395, Role: patient  
[2025-07-22 11:02:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 396, Role: patient  
[2025-07-22 11:02:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 397, Role: patient  
[2025-07-22 11:02:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 398, Role: patient  
[2025-07-22 11:02:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 399, Role: patient  
[2025-07-22 11:02:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 400, Role: patient  
[2025-07-22 11:02:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 401, Role: patient  
[2025-07-22 11:02:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 402, Role: patient  
[2025-07-22 11:02:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 403, Role: patient  
[2025-07-22 11:02:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 404, Role: patient  
[2025-07-22 11:02:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 405, Role: patient  
[2025-07-22 11:02:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 406, Role: patient  
[2025-07-22 11:02:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 407, Role: patient  
[2025-07-22 11:02:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 408, Role: patient  
[2025-07-22 11:02:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 409, Role: patient  
[2025-07-22 11:02:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 410, Role: patient  
[2025-07-22 11:02:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 411, Role: patient  
[2025-07-22 11:02:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 412, Role: patient  
[2025-07-22 11:02:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 413, Role: patient  
[2025-07-22 11:02:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 414, Role: patient  
[2025-07-22 11:02:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 415, Role: patient  
[2025-07-22 11:02:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 416, Role: patient  
[2025-07-22 11:02:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 417, Role: patient  
[2025-07-22 11:02:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 418, Role: patient  
[2025-07-22 11:02:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 419, Role: patient  
[2025-07-22 11:02:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 420, Role: patient  
[2025-07-22 11:02:40] local.INFO: Successfully migrated user peterclift123**@gmail.com → Laravel ID 421, Role: patient  
[2025-07-22 11:02:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 422, Role: patient  
[2025-07-22 11:02:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 423, Role: patient  
[2025-07-22 11:02:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 424, Role: patient  
[2025-07-22 11:02:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 425, Role: patient  
[2025-07-22 11:02:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 426, Role: patient  
[2025-07-22 11:02:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 427, Role: patient  
[2025-07-22 11:02:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 428, Role: patient  
[2025-07-22 11:02:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 429, Role: patient  
[2025-07-22 11:02:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 430, Role: patient  
[2025-07-22 11:02:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 431, Role: patient  
[2025-07-22 11:02:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 432, Role: patient  
[2025-07-22 11:02:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 433, Role: patient  
[2025-07-22 11:02:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 434, Role: patient  
[2025-07-22 11:02:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 435, Role: patient  
[2025-07-22 11:02:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 436, Role: patient  
[2025-07-22 11:02:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 437, Role: patient  
[2025-07-22 11:02:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 438, Role: patient  
[2025-07-22 11:02:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 439, Role: patient  
[2025-07-22 11:02:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 440, Role: patient  
[2025-07-22 11:02:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 441, Role: patient  
[2025-07-22 11:02:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 442, Role: patient  
[2025-07-22 11:02:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 443, Role: patient  
[2025-07-22 11:02:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 444, Role: patient  
[2025-07-22 11:02:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 445, Role: patient  
[2025-07-22 11:02:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 446, Role: patient  
[2025-07-22 11:02:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 447, Role: patient  
[2025-07-22 11:02:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 448, Role: patient  
[2025-07-22 11:02:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 449, Role: patient  
[2025-07-22 11:02:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 450, Role: patient  
[2025-07-22 11:02:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 451, Role: patient  
[2025-07-22 11:02:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 452, Role: patient  
[2025-07-22 11:02:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 453, Role: patient  
[2025-07-22 11:02:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 454, Role: patient  
[2025-07-22 11:02:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 455, Role: patient  
[2025-07-22 11:02:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 456, Role: patient  
[2025-07-22 11:02:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 457, Role: patient  
[2025-07-22 11:02:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 458, Role: patient  
[2025-07-22 11:02:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 459, Role: patient  
[2025-07-22 11:02:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 460, Role: patient  
[2025-07-22 11:02:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 461, Role: patient  
[2025-07-22 11:02:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 462, Role: patient  
[2025-07-22 11:02:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 463, Role: patient  
[2025-07-22 11:02:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 464, Role: patient  
[2025-07-22 11:02:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 465, Role: patient  
[2025-07-22 11:02:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 466, Role: patient  
[2025-07-22 11:02:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 467, Role: patient  
[2025-07-22 11:02:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 468, Role: patient  
[2025-07-22 11:02:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 469, Role: patient  
[2025-07-22 11:02:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 470, Role: patient  
[2025-07-22 11:02:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 471, Role: patient  
[2025-07-22 11:02:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 472, Role: patient  
[2025-07-22 11:02:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 473, Role: patient  
[2025-07-22 11:02:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 474, Role: patient  
[2025-07-22 11:02:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 475, Role: patient  
[2025-07-22 11:02:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 476, Role: patient  
[2025-07-22 11:02:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 477, Role: patient  
[2025-07-22 11:02:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 478, Role: patient  
[2025-07-22 11:02:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 479, Role: patient  
[2025-07-22 11:02:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 480, Role: patient  
[2025-07-22 11:02:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 481, Role: patient  
[2025-07-22 11:02:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 482, Role: patient  
[2025-07-22 11:02:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 483, Role: patient  
[2025-07-22 11:02:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 484, Role: patient  
[2025-07-22 11:02:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 485, Role: patient  
[2025-07-22 11:02:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 486, Role: patient  
[2025-07-22 11:02:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 487, Role: patient  
[2025-07-22 11:02:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 488, Role: patient  
[2025-07-22 11:02:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 489, Role: patient  
[2025-07-22 11:02:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 490, Role: patient  
[2025-07-22 11:02:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 491, Role: patient  
[2025-07-22 11:02:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 492, Role: patient  
[2025-07-22 11:02:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 493, Role: patient  
[2025-07-22 11:02:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 494, Role: patient  
[2025-07-22 11:02:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 495, Role: patient  
[2025-07-22 11:02:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 496, Role: patient  
[2025-07-22 11:02:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 497, Role: patient  
[2025-07-22 11:02:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 498, Role: patient  
[2025-07-22 11:02:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 499, Role: patient  
[2025-07-22 11:02:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 500, Role: patient  
[2025-07-22 11:02:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 501, Role: patient  
[2025-07-22 11:02:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 502, Role: patient  
[2025-07-22 11:02:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 503, Role: patient  
[2025-07-22 11:02:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 504, Role: patient  
[2025-07-22 11:02:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 505, Role: patient  
[2025-07-22 11:02:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 506, Role: patient  
[2025-07-22 11:02:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 507, Role: patient  
[2025-07-22 11:02:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 508, Role: patient  
[2025-07-22 11:02:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 509, Role: patient  
[2025-07-22 11:02:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 510, Role: patient  
[2025-07-22 11:02:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 511, Role: patient  
[2025-07-22 11:02:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 512, Role: patient  
[2025-07-22 11:02:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 513, Role: patient  
[2025-07-22 11:02:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 514, Role: patient  
[2025-07-22 11:03:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 515, Role: patient  
[2025-07-22 11:03:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 516, Role: patient  
[2025-07-22 11:03:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 517, Role: patient  
[2025-07-22 11:03:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 518, Role: patient  
[2025-07-22 11:03:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 519, Role: patient  
[2025-07-22 11:03:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 520, Role: patient  
[2025-07-22 11:03:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 521, Role: patient  
[2025-07-22 11:03:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 522, Role: patient  
[2025-07-22 11:03:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 523, Role: patient  
[2025-07-22 11:03:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 524, Role: patient  
[2025-07-22 11:03:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 525, Role: patient  
[2025-07-22 11:03:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 526, Role: patient  
[2025-07-22 11:03:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 527, Role: patient  
[2025-07-22 11:03:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 528, Role: patient  
[2025-07-22 11:03:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 529, Role: patient  
[2025-07-22 11:03:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 530, Role: patient  
[2025-07-22 11:03:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 531, Role: patient  
[2025-07-22 11:03:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 532, Role: patient  
[2025-07-22 11:03:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 533, Role: patient  
[2025-07-22 11:03:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 534, Role: patient  
[2025-07-22 11:03:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 535, Role: patient  
[2025-07-22 11:03:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 536, Role: patient  
[2025-07-22 11:03:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 537, Role: patient  
[2025-07-22 11:03:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 538, Role: patient  
[2025-07-22 11:03:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 539, Role: patient  
[2025-07-22 11:03:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 540, Role: patient  
[2025-07-22 11:03:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 541, Role: patient  
[2025-07-22 11:03:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 542, Role: patient  
[2025-07-22 11:03:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 543, Role: patient  
[2025-07-22 11:03:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 544, Role: staff  
[2025-07-22 11:03:06] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:03:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 545, Role: clinic_admin  
[2025-07-22 11:03:06] local.INFO: Clinic 14 completed: 395 processed, 129 skipped, 0 errors  
[2025-07-22 11:03:06] local.INFO: Migration completed - Processed: 395, Skipped: 129, Errors: 0  
[2025-07-22 11:03:06] local.INFO: Migration command completed successfully  
[2025-07-22 11:03:21] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 11:03:21] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 11:03:25] local.INFO: WordPress API connection validated  
[2025-07-22 11:03:25] local.INFO: Database connection validated  
[2025-07-22 11:03:25] local.INFO: Starting users migration  
[2025-07-22 11:03:25] local.INFO: Running in DRY RUN mode  
[2025-07-22 11:03:25] local.INFO: Starting users migration for clinic 14  
[2025-07-22 11:03:25] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:03:28] local.INFO: Found 524 users for clinic 14  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would migrate user majetyanupam+_878^<EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:28] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would migrate user peterclift123**@gmail.com with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: DRY RUN: Would <NAME_EMAIL> with role patient → patient  
[2025-07-22 11:03:29] local.INFO: Clinic 14 completed: 524 processed, 0 skipped, 0 errors  
[2025-07-22 11:03:29] local.INFO: Migration completed - Processed: 524, Skipped: 0, Errors: 0  
[2025-07-22 11:03:29] local.INFO: Migration command completed successfully  
[2025-07-22 11:05:07] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 11:05:07] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 11:05:11] local.INFO: WordPress API connection validated  
[2025-07-22 11:05:11] local.INFO: Database connection validated  
[2025-07-22 11:05:11] local.INFO: Starting users migration  
[2025-07-22 11:05:11] local.INFO: Starting users migration for clinic 14  
[2025-07-22 11:05:11] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:05:15] local.INFO: Found 524 users for clinic 14  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: Skipped user majetyanupam+_878^<EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: Skipped user peterclift123**@gmail.com - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:05:15] local.INFO: Clinic 14 completed: 0 processed, 524 skipped, 0 errors  
[2025-07-22 11:05:15] local.INFO: Migration completed - Processed: 0, Skipped: 524, Errors: 0  
[2025-07-22 11:05:15] local.INFO: Migration command completed successfully  
[2025-07-22 11:07:00] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 11:07:00] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 11:07:05] local.INFO: WordPress API connection validated  
[2025-07-22 11:07:05] local.INFO: Database connection validated  
[2025-07-22 11:07:05] local.INFO: Starting users migration  
[2025-07-22 11:07:05] local.INFO: Starting users migration for clinic 14  
[2025-07-22 11:07:05] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:07:12] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 11:07:12] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 11:07:17] local.INFO: WordPress API connection validated  
[2025-07-22 11:07:17] local.INFO: Database connection validated  
[2025-07-22 11:07:17] local.INFO: Starting users migration  
[2025-07-22 11:07:17] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 11:07:22] local.INFO: Starting users migration for clinic 40  
[2025-07-22 11:07:22] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:07:27] local.INFO: Found 5 users for clinic 40  
[2025-07-22 11:07:27] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:27] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:27] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:27] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:27] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:27] local.INFO: Clinic 40 completed: 0 processed, 5 skipped, 0 errors  
[2025-07-22 11:07:27] local.INFO: Starting users migration for clinic 27  
[2025-07-22 11:07:27] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:07:32] local.INFO: Found 1 users for clinic 27  
[2025-07-22 11:07:32] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:32] local.INFO: Clinic 27 completed: 0 processed, 1 skipped, 0 errors  
[2025-07-22 11:07:32] local.INFO: Starting users migration for clinic 26  
[2025-07-22 11:07:32] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:07:37] local.INFO: Found 1 users for clinic 26  
[2025-07-22 11:07:37] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:37] local.INFO: Clinic 26 completed: 0 processed, 1 skipped, 0 errors  
[2025-07-22 11:07:37] local.INFO: Starting users migration for clinic 22  
[2025-07-22 11:07:37] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:07:42] local.INFO: Found 1 users for clinic 22  
[2025-07-22 11:07:42] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:42] local.INFO: Clinic 22 completed: 0 processed, 1 skipped, 0 errors  
[2025-07-22 11:07:42] local.INFO: Starting users migration for clinic 16  
[2025-07-22 11:07:42] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:07:48] local.INFO: Found 7 users for clinic 16  
[2025-07-22 11:07:48] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:48] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:48] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:48] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:48] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:48] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:48] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:48] local.INFO: Clinic 16 completed: 0 processed, 7 skipped, 0 errors  
[2025-07-22 11:07:48] local.INFO: Starting users migration for clinic 15  
[2025-07-22 11:07:48] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:07:53] local.INFO: Found 5 users for clinic 15  
[2025-07-22 11:07:53] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:53] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:53] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:53] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:53] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:07:53] local.INFO: Clinic 15 completed: 0 processed, 5 skipped, 0 errors  
[2025-07-22 11:07:53] local.INFO: Starting users migration for clinic 14  
[2025-07-22 11:07:53] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:08:00] local.INFO: Found 524 users for clinic 14  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: Skipped user majetyanupam+_878^<EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:00] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: Skipped user peterclift123**@gmail.com - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:08:01] local.INFO: Clinic 14 completed: 0 processed, 524 skipped, 0 errors  
[2025-07-22 11:08:01] local.INFO: Starting users migration for clinic 13  
[2025-07-22 11:08:01] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:08:05] local.INFO: Found 2 users for clinic 13  
[2025-07-22 11:08:05] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:08:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 546, Role: provider  
[2025-07-22 11:08:05] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:08:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 547, Role: clinic_admin  
[2025-07-22 11:08:05] local.INFO: Clinic 13 completed: 2 processed, 0 skipped, 0 errors  
[2025-07-22 11:08:05] local.INFO: Starting users migration for clinic 12  
[2025-07-22 11:08:05] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:08:10] local.INFO: Found 36 users for clinic 12  
[2025-07-22 11:08:11] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:08:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 548, Role: provider  
[2025-07-22 11:08:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 549, Role: patient  
[2025-07-22 11:08:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 550, Role: patient  
[2025-07-22 11:08:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 551, Role: patient  
[2025-07-22 11:08:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 552, Role: patient  
[2025-07-22 11:08:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 553, Role: patient  
[2025-07-22 11:08:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 554, Role: patient  
[2025-07-22 11:08:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 555, Role: patient  
[2025-07-22 11:08:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 556, Role: patient  
[2025-07-22 11:08:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 557, Role: patient  
[2025-07-22 11:08:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 558, Role: patient  
[2025-07-22 11:08:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 559, Role: patient  
[2025-07-22 11:08:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 560, Role: patient  
[2025-07-22 11:08:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 561, Role: patient  
[2025-07-22 11:08:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 562, Role: patient  
[2025-07-22 11:08:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 563, Role: patient  
[2025-07-22 11:08:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 564, Role: patient  
[2025-07-22 11:08:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 565, Role: patient  
[2025-07-22 11:08:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 566, Role: patient  
[2025-07-22 11:08:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 567, Role: patient  
[2025-07-22 11:08:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 568, Role: patient  
[2025-07-22 11:08:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 569, Role: patient  
[2025-07-22 11:08:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 570, Role: patient  
[2025-07-22 11:08:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 571, Role: patient  
[2025-07-22 11:08:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 572, Role: patient  
[2025-07-22 11:08:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 573, Role: patient  
[2025-07-22 11:08:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 574, Role: patient  
[2025-07-22 11:08:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 575, Role: patient  
[2025-07-22 11:08:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 576, Role: patient  
[2025-07-22 11:08:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 577, Role: patient  
[2025-07-22 11:08:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 578, Role: patient  
[2025-07-22 11:08:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 579, Role: patient  
[2025-07-22 11:08:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 580, Role: patient  
[2025-07-22 11:08:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 581, Role: patient  
[2025-07-22 11:08:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 582, Role: patient  
[2025-07-22 11:08:18] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:08:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 583, Role: clinic_admin  
[2025-07-22 11:08:18] local.INFO: Clinic 12 completed: 36 processed, 0 skipped, 0 errors  
[2025-07-22 11:08:18] local.INFO: Starting users migration for clinic 11  
[2025-07-22 11:08:18] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:08:24] local.INFO: Found 276 users for clinic 11  
[2025-07-22 11:08:24] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:08:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 584, Role: provider  
[2025-07-22 11:08:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 585, Role: patient  
[2025-07-22 11:08:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 586, Role: patient  
[2025-07-22 11:08:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 587, Role: patient  
[2025-07-22 11:08:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 588, Role: patient  
[2025-07-22 11:08:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 589, Role: patient  
[2025-07-22 11:08:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 590, Role: patient  
[2025-07-22 11:08:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 591, Role: patient  
[2025-07-22 11:08:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 592, Role: patient  
[2025-07-22 11:08:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 593, Role: patient  
[2025-07-22 11:08:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 594, Role: patient  
[2025-07-22 11:08:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 595, Role: patient  
[2025-07-22 11:08:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 596, Role: patient  
[2025-07-22 11:08:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 597, Role: patient  
[2025-07-22 11:08:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 598, Role: patient  
[2025-07-22 11:08:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 599, Role: patient  
[2025-07-22 11:08:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 600, Role: patient  
[2025-07-22 11:08:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 601, Role: patient  
[2025-07-22 11:08:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 602, Role: patient  
[2025-07-22 11:08:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 603, Role: patient  
[2025-07-22 11:08:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 604, Role: patient  
[2025-07-22 11:08:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 605, Role: patient  
[2025-07-22 11:08:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 606, Role: patient  
[2025-07-22 11:08:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 607, Role: patient  
[2025-07-22 11:08:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 608, Role: patient  
[2025-07-22 11:08:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 609, Role: patient  
[2025-07-22 11:08:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 610, Role: patient  
[2025-07-22 11:08:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 611, Role: patient  
[2025-07-22 11:08:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 612, Role: patient  
[2025-07-22 11:08:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 613, Role: patient  
[2025-07-22 11:08:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 614, Role: patient  
[2025-07-22 11:08:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 615, Role: patient  
[2025-07-22 11:08:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 616, Role: patient  
[2025-07-22 11:08:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 617, Role: patient  
[2025-07-22 11:08:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 618, Role: patient  
[2025-07-22 11:08:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 619, Role: patient  
[2025-07-22 11:08:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 620, Role: patient  
[2025-07-22 11:08:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 621, Role: patient  
[2025-07-22 11:08:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 622, Role: patient  
[2025-07-22 11:08:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 623, Role: patient  
[2025-07-22 11:08:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 624, Role: patient  
[2025-07-22 11:08:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 625, Role: patient  
[2025-07-22 11:08:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 626, Role: patient  
[2025-07-22 11:08:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 627, Role: patient  
[2025-07-22 11:08:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 628, Role: patient  
[2025-07-22 11:08:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 629, Role: patient  
[2025-07-22 11:08:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 630, Role: patient  
[2025-07-22 11:08:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 631, Role: patient  
[2025-07-22 11:08:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 632, Role: patient  
[2025-07-22 11:08:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 633, Role: patient  
[2025-07-22 11:08:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 634, Role: patient  
[2025-07-22 11:08:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 635, Role: patient  
[2025-07-22 11:08:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 636, Role: patient  
[2025-07-22 11:08:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 637, Role: patient  
[2025-07-22 11:08:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 638, Role: patient  
[2025-07-22 11:08:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 639, Role: patient  
[2025-07-22 11:08:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 640, Role: patient  
[2025-07-22 11:08:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 641, Role: patient  
[2025-07-22 11:08:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 642, Role: patient  
[2025-07-22 11:08:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 643, Role: patient  
[2025-07-22 11:08:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 644, Role: patient  
[2025-07-22 11:08:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 645, Role: patient  
[2025-07-22 11:08:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 646, Role: patient  
[2025-07-22 11:08:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 647, Role: patient  
[2025-07-22 11:08:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 648, Role: patient  
[2025-07-22 11:08:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 649, Role: patient  
[2025-07-22 11:08:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 650, Role: patient  
[2025-07-22 11:08:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 651, Role: patient  
[2025-07-22 11:08:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 652, Role: patient  
[2025-07-22 11:08:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 653, Role: patient  
[2025-07-22 11:08:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 654, Role: patient  
[2025-07-22 11:08:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 655, Role: patient  
[2025-07-22 11:08:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 656, Role: patient  
[2025-07-22 11:08:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 657, Role: patient  
[2025-07-22 11:08:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 658, Role: patient  
[2025-07-22 11:08:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 659, Role: patient  
[2025-07-22 11:08:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 660, Role: patient  
[2025-07-22 11:08:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 661, Role: patient  
[2025-07-22 11:08:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 662, Role: patient  
[2025-07-22 11:08:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 663, Role: patient  
[2025-07-22 11:08:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 664, Role: patient  
[2025-07-22 11:08:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 665, Role: patient  
[2025-07-22 11:08:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 666, Role: patient  
[2025-07-22 11:08:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 667, Role: patient  
[2025-07-22 11:08:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 668, Role: patient  
[2025-07-22 11:08:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 669, Role: patient  
[2025-07-22 11:08:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 670, Role: patient  
[2025-07-22 11:08:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 671, Role: patient  
[2025-07-22 11:08:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 672, Role: patient  
[2025-07-22 11:08:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 673, Role: patient  
[2025-07-22 11:08:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 674, Role: patient  
[2025-07-22 11:08:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 675, Role: patient  
[2025-07-22 11:08:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 676, Role: patient  
[2025-07-22 11:08:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 677, Role: patient  
[2025-07-22 11:08:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 678, Role: patient  
[2025-07-22 11:08:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 679, Role: patient  
[2025-07-22 11:08:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 680, Role: patient  
[2025-07-22 11:08:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 681, Role: patient  
[2025-07-22 11:08:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 682, Role: patient  
[2025-07-22 11:08:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 683, Role: patient  
[2025-07-22 11:08:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 684, Role: patient  
[2025-07-22 11:08:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 685, Role: patient  
[2025-07-22 11:08:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 686, Role: patient  
[2025-07-22 11:08:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 687, Role: patient  
[2025-07-22 11:08:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 688, Role: patient  
[2025-07-22 11:08:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 689, Role: patient  
[2025-07-22 11:08:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 690, Role: patient  
[2025-07-22 11:08:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 691, Role: patient  
[2025-07-22 11:08:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 692, Role: patient  
[2025-07-22 11:08:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 693, Role: patient  
[2025-07-22 11:08:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 694, Role: patient  
[2025-07-22 11:08:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 695, Role: patient  
[2025-07-22 11:08:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 696, Role: patient  
[2025-07-22 11:08:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 697, Role: patient  
[2025-07-22 11:08:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 698, Role: patient  
[2025-07-22 11:08:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 699, Role: patient  
[2025-07-22 11:08:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 700, Role: patient  
[2025-07-22 11:08:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 701, Role: patient  
[2025-07-22 11:08:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 702, Role: patient  
[2025-07-22 11:08:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 703, Role: patient  
[2025-07-22 11:08:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 704, Role: patient  
[2025-07-22 11:08:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 705, Role: patient  
[2025-07-22 11:08:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 706, Role: patient  
[2025-07-22 11:08:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 707, Role: patient  
[2025-07-22 11:08:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 708, Role: patient  
[2025-07-22 11:08:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 709, Role: patient  
[2025-07-22 11:08:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 710, Role: patient  
[2025-07-22 11:08:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 711, Role: patient  
[2025-07-22 11:08:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 712, Role: patient  
[2025-07-22 11:08:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 713, Role: patient  
[2025-07-22 11:08:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 714, Role: patient  
[2025-07-22 11:08:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 715, Role: patient  
[2025-07-22 11:08:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 716, Role: patient  
[2025-07-22 11:08:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 717, Role: patient  
[2025-07-22 11:08:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 718, Role: patient  
[2025-07-22 11:08:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 719, Role: patient  
[2025-07-22 11:08:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 720, Role: patient  
[2025-07-22 11:08:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 721, Role: patient  
[2025-07-22 11:08:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 722, Role: patient  
[2025-07-22 11:08:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 723, Role: patient  
[2025-07-22 11:08:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 724, Role: patient  
[2025-07-22 11:08:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 725, Role: patient  
[2025-07-22 11:08:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 726, Role: patient  
[2025-07-22 11:08:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 727, Role: patient  
[2025-07-22 11:08:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 728, Role: patient  
[2025-07-22 11:08:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 729, Role: patient  
[2025-07-22 11:08:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 730, Role: patient  
[2025-07-22 11:08:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 731, Role: patient  
[2025-07-22 11:08:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 732, Role: patient  
[2025-07-22 11:08:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 733, Role: patient  
[2025-07-22 11:08:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 734, Role: patient  
[2025-07-22 11:08:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 735, Role: patient  
[2025-07-22 11:08:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 736, Role: patient  
[2025-07-22 11:08:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 737, Role: patient  
[2025-07-22 11:08:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 738, Role: patient  
[2025-07-22 11:08:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 739, Role: patient  
[2025-07-22 11:08:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 740, Role: patient  
[2025-07-22 11:08:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 741, Role: patient  
[2025-07-22 11:08:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 742, Role: patient  
[2025-07-22 11:08:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 743, Role: patient  
[2025-07-22 11:08:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 744, Role: patient  
[2025-07-22 11:08:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 745, Role: patient  
[2025-07-22 11:08:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 746, Role: patient  
[2025-07-22 11:08:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 747, Role: patient  
[2025-07-22 11:08:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 748, Role: patient  
[2025-07-22 11:08:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 749, Role: patient  
[2025-07-22 11:08:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 750, Role: patient  
[2025-07-22 11:08:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 751, Role: patient  
[2025-07-22 11:08:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 752, Role: patient  
[2025-07-22 11:08:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 753, Role: patient  
[2025-07-22 11:08:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 754, Role: patient  
[2025-07-22 11:08:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 755, Role: patient  
[2025-07-22 11:08:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 756, Role: patient  
[2025-07-22 11:08:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 757, Role: patient  
[2025-07-22 11:08:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 758, Role: patient  
[2025-07-22 11:08:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 759, Role: patient  
[2025-07-22 11:08:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 760, Role: patient  
[2025-07-22 11:09:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 761, Role: patient  
[2025-07-22 11:09:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 762, Role: patient  
[2025-07-22 11:09:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 763, Role: patient  
[2025-07-22 11:09:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 764, Role: patient  
[2025-07-22 11:09:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 765, Role: patient  
[2025-07-22 11:09:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 766, Role: patient  
[2025-07-22 11:09:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 767, Role: patient  
[2025-07-22 11:09:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 768, Role: patient  
[2025-07-22 11:09:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 769, Role: patient  
[2025-07-22 11:09:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 770, Role: patient  
[2025-07-22 11:09:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 771, Role: patient  
[2025-07-22 11:09:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 772, Role: patient  
[2025-07-22 11:09:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 773, Role: patient  
[2025-07-22 11:09:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 774, Role: patient  
[2025-07-22 11:09:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 775, Role: patient  
[2025-07-22 11:09:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 776, Role: patient  
[2025-07-22 11:09:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 777, Role: patient  
[2025-07-22 11:09:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 778, Role: patient  
[2025-07-22 11:09:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 779, Role: patient  
[2025-07-22 11:09:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 780, Role: patient  
[2025-07-22 11:09:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 781, Role: patient  
[2025-07-22 11:09:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 782, Role: patient  
[2025-07-22 11:09:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 783, Role: patient  
[2025-07-22 11:09:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 784, Role: patient  
[2025-07-22 11:09:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 785, Role: patient  
[2025-07-22 11:09:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 786, Role: patient  
[2025-07-22 11:09:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 787, Role: patient  
[2025-07-22 11:09:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 788, Role: patient  
[2025-07-22 11:09:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 789, Role: patient  
[2025-07-22 11:09:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 790, Role: patient  
[2025-07-22 11:09:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 791, Role: patient  
[2025-07-22 11:09:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 792, Role: patient  
[2025-07-22 11:09:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 793, Role: patient  
[2025-07-22 11:09:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 794, Role: patient  
[2025-07-22 11:09:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 795, Role: patient  
[2025-07-22 11:09:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 796, Role: patient  
[2025-07-22 11:09:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 797, Role: patient  
[2025-07-22 11:09:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 798, Role: patient  
[2025-07-22 11:09:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 799, Role: patient  
[2025-07-22 11:09:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 800, Role: patient  
[2025-07-22 11:09:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 801, Role: patient  
[2025-07-22 11:09:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 802, Role: patient  
[2025-07-22 11:09:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 803, Role: patient  
[2025-07-22 11:09:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 804, Role: patient  
[2025-07-22 11:09:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 805, Role: patient  
[2025-07-22 11:09:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 806, Role: patient  
[2025-07-22 11:09:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 807, Role: patient  
[2025-07-22 11:09:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 808, Role: patient  
[2025-07-22 11:09:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 809, Role: patient  
[2025-07-22 11:09:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 810, Role: patient  
[2025-07-22 11:09:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 811, Role: patient  
[2025-07-22 11:09:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 812, Role: patient  
[2025-07-22 11:09:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 813, Role: patient  
[2025-07-22 11:09:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 814, Role: patient  
[2025-07-22 11:09:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 815, Role: patient  
[2025-07-22 11:09:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 816, Role: patient  
[2025-07-22 11:09:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 817, Role: patient  
[2025-07-22 11:09:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 818, Role: patient  
[2025-07-22 11:09:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 819, Role: patient  
[2025-07-22 11:09:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 820, Role: patient  
[2025-07-22 11:09:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 821, Role: patient  
[2025-07-22 11:09:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 822, Role: patient  
[2025-07-22 11:09:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 823, Role: patient  
[2025-07-22 11:09:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 824, Role: patient  
[2025-07-22 11:09:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 825, Role: patient  
[2025-07-22 11:09:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 826, Role: patient  
[2025-07-22 11:09:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 827, Role: patient  
[2025-07-22 11:09:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 828, Role: patient  
[2025-07-22 11:09:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 829, Role: patient  
[2025-07-22 11:09:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 830, Role: patient  
[2025-07-22 11:09:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 831, Role: patient  
[2025-07-22 11:09:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 832, Role: patient  
[2025-07-22 11:09:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 833, Role: patient  
[2025-07-22 11:09:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 834, Role: patient  
[2025-07-22 11:09:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 835, Role: patient  
[2025-07-22 11:09:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 836, Role: patient  
[2025-07-22 11:09:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 837, Role: patient  
[2025-07-22 11:09:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 838, Role: patient  
[2025-07-22 11:09:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 839, Role: patient  
[2025-07-22 11:09:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 840, Role: patient  
[2025-07-22 11:09:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 841, Role: patient  
[2025-07-22 11:09:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 842, Role: patient  
[2025-07-22 11:09:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 843, Role: patient  
[2025-07-22 11:09:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 844, Role: patient  
[2025-07-22 11:09:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 845, Role: patient  
[2025-07-22 11:09:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 846, Role: patient  
[2025-07-22 11:09:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 847, Role: patient  
[2025-07-22 11:09:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 848, Role: patient  
[2025-07-22 11:09:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 849, Role: patient  
[2025-07-22 11:09:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 850, Role: patient  
[2025-07-22 11:09:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 851, Role: patient  
[2025-07-22 11:09:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 852, Role: patient  
[2025-07-22 11:09:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 853, Role: patient  
[2025-07-22 11:09:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 854, Role: patient  
[2025-07-22 11:09:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 855, Role: patient  
[2025-07-22 11:09:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 856, Role: patient  
[2025-07-22 11:09:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 857, Role: patient  
[2025-07-22 11:09:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 858, Role: patient  
[2025-07-22 11:09:20] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:09:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 859, Role: clinic_admin  
[2025-07-22 11:09:20] local.INFO: Clinic 11 completed: 276 processed, 0 skipped, 0 errors  
[2025-07-22 11:09:20] local.INFO: Starting users migration for clinic 10  
[2025-07-22 11:09:20] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:09:25] local.INFO: Found 3 users for clinic 10  
[2025-07-22 11:09:25] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:09:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 860, Role: provider  
[2025-07-22 11:09:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 861, Role: patient  
[2025-07-22 11:09:25] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:09:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 862, Role: clinic_admin  
[2025-07-22 11:09:25] local.INFO: Clinic 10 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 11:09:25] local.INFO: Starting users migration for clinic 9  
[2025-07-22 11:09:25] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:09:30] local.INFO: Found 6 users for clinic 9  
[2025-07-22 11:09:30] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:09:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 863, Role: provider  
[2025-07-22 11:09:30] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:09:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 864, Role: provider  
[2025-07-22 11:09:30] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:09:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 865, Role: provider  
[2025-07-22 11:09:31] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:09:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 866, Role: provider  
[2025-07-22 11:09:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 867, Role: patient  
[2025-07-22 11:09:31] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:09:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 868, Role: clinic_admin  
[2025-07-22 11:09:31] local.INFO: Clinic 9 completed: 6 processed, 0 skipped, 0 errors  
[2025-07-22 11:09:31] local.INFO: Starting users migration for clinic 8  
[2025-07-22 11:09:31] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:09:37] local.INFO: Found 11 users for clinic 8  
[2025-07-22 11:09:37] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:09:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 869, Role: provider  
[2025-07-22 11:09:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 870, Role: patient  
[2025-07-22 11:09:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 871, Role: patient  
[2025-07-22 11:09:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 872, Role: patient  
[2025-07-22 11:09:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 873, Role: patient  
[2025-07-22 11:09:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 874, Role: patient  
[2025-07-22 11:09:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 875, Role: patient  
[2025-07-22 11:09:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 876, Role: patient  
[2025-07-22 11:09:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 877, Role: patient  
[2025-07-22 11:09:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 878, Role: patient  
[2025-07-22 11:09:39] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:09:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 879, Role: clinic_admin  
[2025-07-22 11:09:39] local.INFO: Clinic 8 completed: 11 processed, 0 skipped, 0 errors  
[2025-07-22 11:09:39] local.INFO: Starting users migration for clinic 6  
[2025-07-22 11:09:39] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:09:45] local.INFO: Found 3 users for clinic 6  
[2025-07-22 11:09:45] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:09:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 880, Role: provider  
[2025-07-22 11:09:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 881, Role: patient  
[2025-07-22 11:09:45] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:09:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 882, Role: clinic_admin  
[2025-07-22 11:09:45] local.INFO: Clinic 6 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 11:09:45] local.INFO: Starting users migration for clinic 5  
[2025-07-22 11:09:45] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:09:52] local.INFO: Found 16 users for clinic 5  
[2025-07-22 11:09:52] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:09:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 883, Role: provider  
[2025-07-22 11:09:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 884, Role: patient  
[2025-07-22 11:09:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 885, Role: patient  
[2025-07-22 11:09:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 886, Role: patient  
[2025-07-22 11:09:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 887, Role: patient  
[2025-07-22 11:09:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 888, Role: patient  
[2025-07-22 11:09:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 889, Role: patient  
[2025-07-22 11:09:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 890, Role: patient  
[2025-07-22 11:09:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 891, Role: patient  
[2025-07-22 11:09:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 892, Role: patient  
[2025-07-22 11:09:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 893, Role: patient  
[2025-07-22 11:09:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 894, Role: patient  
[2025-07-22 11:09:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 895, Role: patient  
[2025-07-22 11:09:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 896, Role: patient  
[2025-07-22 11:09:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 897, Role: patient  
[2025-07-22 11:09:55] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:09:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 898, Role: clinic_admin  
[2025-07-22 11:09:55] local.INFO: Clinic 5 completed: 16 processed, 0 skipped, 0 errors  
[2025-07-22 11:09:55] local.INFO: Starting users migration for clinic 4  
[2025-07-22 11:09:55] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:10:00] local.INFO: No users found for clinic 4  
[2025-07-22 11:10:00] local.INFO: Starting users migration for clinic 2  
[2025-07-22 11:10:00] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:10:05] local.INFO: Found 7 users for clinic 2  
[2025-07-22 11:10:06] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:10:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 899, Role: provider  
[2025-07-22 11:10:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 900, Role: patient  
[2025-07-22 11:10:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 901, Role: patient  
[2025-07-22 11:10:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 902, Role: patient  
[2025-07-22 11:10:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 903, Role: patient  
[2025-07-22 11:10:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 904, Role: patient  
[2025-07-22 11:10:07] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:10:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 905, Role: clinic_admin  
[2025-07-22 11:10:07] local.INFO: Clinic 2 completed: 7 processed, 0 skipped, 0 errors  
[2025-07-22 11:10:07] local.INFO: Starting users migration for clinic 1  
[2025-07-22 11:10:07] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:10:11] local.INFO: Found 54 users for clinic 1  
[2025-07-22 11:10:11] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:10:11] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:10:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 906, Role: provider  
[2025-07-22 11:10:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 907, Role: patient  
[2025-07-22 11:10:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 908, Role: patient  
[2025-07-22 11:10:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 909, Role: patient  
[2025-07-22 11:10:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 910, Role: patient  
[2025-07-22 11:10:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 911, Role: patient  
[2025-07-22 11:10:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 912, Role: patient  
[2025-07-22 11:10:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 913, Role: patient  
[2025-07-22 11:10:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 914, Role: patient  
[2025-07-22 11:10:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 915, Role: patient  
[2025-07-22 11:10:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 916, Role: patient  
[2025-07-22 11:10:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 917, Role: patient  
[2025-07-22 11:10:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 918, Role: patient  
[2025-07-22 11:10:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 919, Role: patient  
[2025-07-22 11:10:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 920, Role: patient  
[2025-07-22 11:10:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 921, Role: patient  
[2025-07-22 11:10:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 922, Role: patient  
[2025-07-22 11:10:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 923, Role: patient  
[2025-07-22 11:10:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 924, Role: patient  
[2025-07-22 11:10:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 925, Role: patient  
[2025-07-22 11:10:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 926, Role: patient  
[2025-07-22 11:10:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 927, Role: patient  
[2025-07-22 11:10:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 928, Role: patient  
[2025-07-22 11:10:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 929, Role: patient  
[2025-07-22 11:10:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 930, Role: patient  
[2025-07-22 11:10:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 931, Role: patient  
[2025-07-22 11:10:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 932, Role: patient  
[2025-07-22 11:10:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 933, Role: patient  
[2025-07-22 11:10:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 934, Role: patient  
[2025-07-22 11:10:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 935, Role: patient  
[2025-07-22 11:10:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 936, Role: patient  
[2025-07-22 11:10:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 937, Role: patient  
[2025-07-22 11:10:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 938, Role: patient  
[2025-07-22 11:10:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 939, Role: patient  
[2025-07-22 11:10:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 940, Role: patient  
[2025-07-22 11:10:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 941, Role: patient  
[2025-07-22 11:10:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 942, Role: patient  
[2025-07-22 11:10:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 943, Role: patient  
[2025-07-22 11:10:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 944, Role: patient  
[2025-07-22 11:10:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 945, Role: patient  
[2025-07-22 11:10:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 946, Role: patient  
[2025-07-22 11:10:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 947, Role: patient  
[2025-07-22 11:10:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 948, Role: patient  
[2025-07-22 11:10:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 949, Role: patient  
[2025-07-22 11:10:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 950, Role: patient  
[2025-07-22 11:10:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 951, Role: patient  
[2025-07-22 11:10:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 952, Role: patient  
[2025-07-22 11:10:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 953, Role: patient  
[2025-07-22 11:10:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 954, Role: patient  
[2025-07-22 11:10:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 955, Role: patient  
[2025-07-22 11:10:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 956, Role: patient  
[2025-07-22 11:10:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 957, Role: patient  
[2025-07-22 11:10:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 958, Role: patient  
[2025-07-22 11:10:22] local.INFO: Clinic 1 completed: 53 processed, 1 skipped, 0 errors  
[2025-07-22 11:10:22] local.INFO: Migration completed - Processed: 413, Skipped: 545, Errors: 0  
[2025-07-22 11:10:22] local.INFO: Migration command completed successfully  
[2025-07-22 11:16:26] local.INFO: Starting migration command: migratewp:users  
[2025-07-22 11:16:26] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 11:16:31] local.INFO: WordPress API connection validated  
[2025-07-22 11:16:31] local.INFO: Database connection validated  
[2025-07-22 11:16:31] local.INFO: Starting users migration  
[2025-07-22 11:16:31] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 11:16:37] local.INFO: Starting users migration for clinic 40  
[2025-07-22 11:16:37] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:16:43] local.INFO: Found 5 users for clinic 40  
[2025-07-22 11:16:43] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:16:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 959, Role: provider  
[2025-07-22 11:16:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 960, Role: patient  
[2025-07-22 11:16:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 961, Role: patient  
[2025-07-22 11:16:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 962, Role: staff  
[2025-07-22 11:16:44] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:16:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 963, Role: clinic_admin  
[2025-07-22 11:16:44] local.INFO: Clinic 40 completed: 5 processed, 0 skipped, 0 errors  
[2025-07-22 11:16:44] local.INFO: Starting users migration for clinic 27  
[2025-07-22 11:16:44] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:16:50] local.INFO: Found 1 users for clinic 27  
[2025-07-22 11:16:50] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:16:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 964, Role: clinic_admin  
[2025-07-22 11:16:50] local.INFO: Clinic 27 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 11:16:50] local.INFO: Starting users migration for clinic 26  
[2025-07-22 11:16:50] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:16:55] local.INFO: Found 1 users for clinic 26  
[2025-07-22 11:16:56] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:16:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 965, Role: clinic_admin  
[2025-07-22 11:16:56] local.INFO: Clinic 26 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 11:16:56] local.INFO: Starting users migration for clinic 22  
[2025-07-22 11:16:56] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:17:01] local.INFO: Found 1 users for clinic 22  
[2025-07-22 11:17:02] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 966, Role: clinic_admin  
[2025-07-22 11:17:02] local.INFO: Clinic 22 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 11:17:02] local.INFO: Starting users migration for clinic 16  
[2025-07-22 11:17:02] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:17:06] local.INFO: Found 7 users for clinic 16  
[2025-07-22 11:17:06] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 967, Role: provider  
[2025-07-22 11:17:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 968, Role: patient  
[2025-07-22 11:17:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 969, Role: patient  
[2025-07-22 11:17:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 970, Role: patient  
[2025-07-22 11:17:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 971, Role: patient  
[2025-07-22 11:17:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 972, Role: patient  
[2025-07-22 11:17:08] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 973, Role: clinic_admin  
[2025-07-22 11:17:08] local.INFO: Clinic 16 completed: 7 processed, 0 skipped, 0 errors  
[2025-07-22 11:17:08] local.INFO: Starting users migration for clinic 15  
[2025-07-22 11:17:08] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:17:12] local.INFO: Found 5 users for clinic 15  
[2025-07-22 11:17:12] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 974, Role: provider  
[2025-07-22 11:17:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 975, Role: patient  
[2025-07-22 11:17:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 976, Role: patient  
[2025-07-22 11:17:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 977, Role: patient  
[2025-07-22 11:17:13] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 978, Role: clinic_admin  
[2025-07-22 11:17:13] local.INFO: Clinic 15 completed: 5 processed, 0 skipped, 0 errors  
[2025-07-22 11:17:13] local.INFO: Starting users migration for clinic 14  
[2025-07-22 11:17:13] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:17:19] local.INFO: Found 524 users for clinic 14  
[2025-07-22 11:17:20] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 979, Role: provider  
[2025-07-22 11:17:20] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 980, Role: provider  
[2025-07-22 11:17:20] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 981, Role: provider  
[2025-07-22 11:17:20] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 982, Role: provider  
[2025-07-22 11:17:20] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 983, Role: provider  
[2025-07-22 11:17:21] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 984, Role: provider  
[2025-07-22 11:17:21] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 985, Role: provider  
[2025-07-22 11:17:21] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 986, Role: provider  
[2025-07-22 11:17:21] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 987, Role: provider  
[2025-07-22 11:17:21] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 988, Role: provider  
[2025-07-22 11:17:22] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 989, Role: provider  
[2025-07-22 11:17:22] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 990, Role: provider  
[2025-07-22 11:17:22] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 991, Role: provider  
[2025-07-22 11:17:22] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 992, Role: provider  
[2025-07-22 11:17:22] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 993, Role: provider  
[2025-07-22 11:17:23] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 994, Role: provider  
[2025-07-22 11:17:23] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 995, Role: provider  
[2025-07-22 11:17:23] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 996, Role: provider  
[2025-07-22 11:17:23] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 997, Role: provider  
[2025-07-22 11:17:24] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 998, Role: provider  
[2025-07-22 11:17:24] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:17:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 999, Role: provider  
[2025-07-22 11:17:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1000, Role: patient  
[2025-07-22 11:17:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1001, Role: patient  
[2025-07-22 11:17:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1002, Role: patient  
[2025-07-22 11:17:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1003, Role: patient  
[2025-07-22 11:17:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1004, Role: patient  
[2025-07-22 11:17:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1005, Role: patient  
[2025-07-22 11:17:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1006, Role: patient  
[2025-07-22 11:17:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1007, Role: patient  
[2025-07-22 11:17:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1008, Role: patient  
[2025-07-22 11:17:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1009, Role: patient  
[2025-07-22 11:17:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1010, Role: patient  
[2025-07-22 11:17:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1011, Role: patient  
[2025-07-22 11:17:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1012, Role: patient  
[2025-07-22 11:17:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1013, Role: patient  
[2025-07-22 11:17:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1014, Role: patient  
[2025-07-22 11:17:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1015, Role: patient  
[2025-07-22 11:17:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1016, Role: patient  
[2025-07-22 11:17:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1017, Role: patient  
[2025-07-22 11:17:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1018, Role: patient  
[2025-07-22 11:17:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1019, Role: patient  
[2025-07-22 11:17:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1020, Role: patient  
[2025-07-22 11:17:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1021, Role: patient  
[2025-07-22 11:17:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1022, Role: patient  
[2025-07-22 11:17:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1023, Role: patient  
[2025-07-22 11:17:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1024, Role: patient  
[2025-07-22 11:17:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1025, Role: patient  
[2025-07-22 11:17:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1026, Role: patient  
[2025-07-22 11:17:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1027, Role: patient  
[2025-07-22 11:17:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1028, Role: patient  
[2025-07-22 11:17:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1029, Role: patient  
[2025-07-22 11:17:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1030, Role: patient  
[2025-07-22 11:17:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1031, Role: patient  
[2025-07-22 11:17:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1032, Role: patient  
[2025-07-22 11:17:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1033, Role: patient  
[2025-07-22 11:17:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1034, Role: patient  
[2025-07-22 11:17:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1035, Role: patient  
[2025-07-22 11:17:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1036, Role: patient  
[2025-07-22 11:17:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1037, Role: patient  
[2025-07-22 11:17:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1038, Role: patient  
[2025-07-22 11:17:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1039, Role: patient  
[2025-07-22 11:17:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1040, Role: patient  
[2025-07-22 11:17:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1041, Role: patient  
[2025-07-22 11:17:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1042, Role: patient  
[2025-07-22 11:17:33] local.INFO: Successfully migrated user majetyanupam+_878^<EMAIL> → Laravel ID 1043, Role: patient  
[2025-07-22 11:17:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1044, Role: patient  
[2025-07-22 11:17:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1045, Role: patient  
[2025-07-22 11:17:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1046, Role: patient  
[2025-07-22 11:17:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1047, Role: patient  
[2025-07-22 11:17:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1048, Role: patient  
[2025-07-22 11:17:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1049, Role: patient  
[2025-07-22 11:17:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1050, Role: patient  
[2025-07-22 11:17:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1051, Role: patient  
[2025-07-22 11:17:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1052, Role: patient  
[2025-07-22 11:17:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1053, Role: patient  
[2025-07-22 11:17:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1054, Role: patient  
[2025-07-22 11:17:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1055, Role: patient  
[2025-07-22 11:17:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1056, Role: patient  
[2025-07-22 11:17:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1057, Role: patient  
[2025-07-22 11:17:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1058, Role: patient  
[2025-07-22 11:17:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1059, Role: patient  
[2025-07-22 11:17:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1060, Role: patient  
[2025-07-22 11:17:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1061, Role: patient  
[2025-07-22 11:17:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1062, Role: patient  
[2025-07-22 11:17:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1063, Role: patient  
[2025-07-22 11:17:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1064, Role: patient  
[2025-07-22 11:17:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1065, Role: patient  
[2025-07-22 11:17:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1066, Role: patient  
[2025-07-22 11:17:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1067, Role: patient  
[2025-07-22 11:17:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1068, Role: patient  
[2025-07-22 11:17:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1069, Role: patient  
[2025-07-22 11:17:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1070, Role: patient  
[2025-07-22 11:17:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1071, Role: patient  
[2025-07-22 11:17:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1072, Role: patient  
[2025-07-22 11:17:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1073, Role: patient  
[2025-07-22 11:17:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1074, Role: patient  
[2025-07-22 11:17:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1075, Role: patient  
[2025-07-22 11:17:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1076, Role: patient  
[2025-07-22 11:17:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1077, Role: patient  
[2025-07-22 11:17:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1078, Role: patient  
[2025-07-22 11:17:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1079, Role: patient  
[2025-07-22 11:17:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1080, Role: patient  
[2025-07-22 11:17:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1081, Role: patient  
[2025-07-22 11:17:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1082, Role: patient  
[2025-07-22 11:17:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1083, Role: patient  
[2025-07-22 11:17:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1084, Role: patient  
[2025-07-22 11:17:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1085, Role: patient  
[2025-07-22 11:17:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1086, Role: patient  
[2025-07-22 11:17:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1087, Role: patient  
[2025-07-22 11:17:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1088, Role: patient  
[2025-07-22 11:17:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1089, Role: patient  
[2025-07-22 11:17:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1090, Role: patient  
[2025-07-22 11:17:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1091, Role: patient  
[2025-07-22 11:17:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1092, Role: patient  
[2025-07-22 11:17:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1093, Role: patient  
[2025-07-22 11:17:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1094, Role: patient  
[2025-07-22 11:17:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1095, Role: patient  
[2025-07-22 11:17:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1096, Role: patient  
[2025-07-22 11:17:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1097, Role: patient  
[2025-07-22 11:17:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1098, Role: patient  
[2025-07-22 11:17:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1099, Role: patient  
[2025-07-22 11:17:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1100, Role: patient  
[2025-07-22 11:17:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1101, Role: patient  
[2025-07-22 11:17:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1102, Role: patient  
[2025-07-22 11:17:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1103, Role: patient  
[2025-07-22 11:17:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1104, Role: patient  
[2025-07-22 11:17:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1105, Role: patient  
[2025-07-22 11:17:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1106, Role: patient  
[2025-07-22 11:17:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1107, Role: patient  
[2025-07-22 11:17:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1108, Role: patient  
[2025-07-22 11:17:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1109, Role: patient  
[2025-07-22 11:17:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1110, Role: patient  
[2025-07-22 11:17:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1111, Role: patient  
[2025-07-22 11:17:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1112, Role: patient  
[2025-07-22 11:17:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1113, Role: patient  
[2025-07-22 11:17:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1114, Role: patient  
[2025-07-22 11:17:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1115, Role: patient  
[2025-07-22 11:17:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1116, Role: patient  
[2025-07-22 11:17:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1117, Role: patient  
[2025-07-22 11:17:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1118, Role: patient  
[2025-07-22 11:17:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1119, Role: patient  
[2025-07-22 11:17:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1120, Role: patient  
[2025-07-22 11:17:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1121, Role: patient  
[2025-07-22 11:17:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1122, Role: patient  
[2025-07-22 11:17:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1123, Role: patient  
[2025-07-22 11:17:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1124, Role: patient  
[2025-07-22 11:17:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1125, Role: patient  
[2025-07-22 11:17:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1126, Role: patient  
[2025-07-22 11:17:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1127, Role: patient  
[2025-07-22 11:17:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1128, Role: patient  
[2025-07-22 11:17:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1129, Role: patient  
[2025-07-22 11:17:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1130, Role: patient  
[2025-07-22 11:17:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1131, Role: patient  
[2025-07-22 11:17:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1132, Role: patient  
[2025-07-22 11:17:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1133, Role: patient  
[2025-07-22 11:17:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1134, Role: patient  
[2025-07-22 11:17:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1135, Role: patient  
[2025-07-22 11:17:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1136, Role: patient  
[2025-07-22 11:17:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1137, Role: patient  
[2025-07-22 11:17:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1138, Role: patient  
[2025-07-22 11:17:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1139, Role: patient  
[2025-07-22 11:17:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1140, Role: patient  
[2025-07-22 11:17:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1141, Role: patient  
[2025-07-22 11:17:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1142, Role: patient  
[2025-07-22 11:17:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1143, Role: patient  
[2025-07-22 11:17:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1144, Role: patient  
[2025-07-22 11:17:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1145, Role: patient  
[2025-07-22 11:17:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1146, Role: patient  
[2025-07-22 11:17:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1147, Role: patient  
[2025-07-22 11:17:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1148, Role: patient  
[2025-07-22 11:17:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1149, Role: patient  
[2025-07-22 11:17:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1150, Role: patient  
[2025-07-22 11:17:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1151, Role: patient  
[2025-07-22 11:17:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1152, Role: patient  
[2025-07-22 11:17:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1153, Role: patient  
[2025-07-22 11:17:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1154, Role: patient  
[2025-07-22 11:17:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1155, Role: patient  
[2025-07-22 11:17:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1156, Role: patient  
[2025-07-22 11:17:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1157, Role: patient  
[2025-07-22 11:17:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1158, Role: patient  
[2025-07-22 11:17:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1159, Role: patient  
[2025-07-22 11:17:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1160, Role: patient  
[2025-07-22 11:17:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1161, Role: patient  
[2025-07-22 11:17:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1162, Role: patient  
[2025-07-22 11:17:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1163, Role: patient  
[2025-07-22 11:17:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1164, Role: patient  
[2025-07-22 11:17:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1165, Role: patient  
[2025-07-22 11:17:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1166, Role: patient  
[2025-07-22 11:17:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1167, Role: patient  
[2025-07-22 11:17:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1168, Role: patient  
[2025-07-22 11:17:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1169, Role: patient  
[2025-07-22 11:17:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1170, Role: patient  
[2025-07-22 11:17:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1171, Role: patient  
[2025-07-22 11:17:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1172, Role: patient  
[2025-07-22 11:17:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1173, Role: patient  
[2025-07-22 11:17:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1174, Role: patient  
[2025-07-22 11:17:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1175, Role: patient  
[2025-07-22 11:18:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1176, Role: patient  
[2025-07-22 11:18:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1177, Role: patient  
[2025-07-22 11:18:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1178, Role: patient  
[2025-07-22 11:18:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1179, Role: patient  
[2025-07-22 11:18:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1180, Role: patient  
[2025-07-22 11:18:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1181, Role: patient  
[2025-07-22 11:18:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1182, Role: patient  
[2025-07-22 11:18:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1183, Role: patient  
[2025-07-22 11:18:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1184, Role: patient  
[2025-07-22 11:18:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1185, Role: patient  
[2025-07-22 11:18:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1186, Role: patient  
[2025-07-22 11:18:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1187, Role: patient  
[2025-07-22 11:18:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1188, Role: patient  
[2025-07-22 11:18:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1189, Role: patient  
[2025-07-22 11:18:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1190, Role: patient  
[2025-07-22 11:18:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1191, Role: patient  
[2025-07-22 11:18:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1192, Role: patient  
[2025-07-22 11:18:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1193, Role: patient  
[2025-07-22 11:18:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1194, Role: patient  
[2025-07-22 11:18:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1195, Role: patient  
[2025-07-22 11:18:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1196, Role: patient  
[2025-07-22 11:18:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1197, Role: patient  
[2025-07-22 11:18:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1198, Role: patient  
[2025-07-22 11:18:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1199, Role: patient  
[2025-07-22 11:18:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1200, Role: patient  
[2025-07-22 11:18:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1201, Role: patient  
[2025-07-22 11:18:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1202, Role: patient  
[2025-07-22 11:18:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1203, Role: patient  
[2025-07-22 11:18:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1204, Role: patient  
[2025-07-22 11:18:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1205, Role: patient  
[2025-07-22 11:18:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1206, Role: patient  
[2025-07-22 11:18:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1207, Role: patient  
[2025-07-22 11:18:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1208, Role: patient  
[2025-07-22 11:18:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1209, Role: patient  
[2025-07-22 11:18:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1210, Role: patient  
[2025-07-22 11:18:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1211, Role: patient  
[2025-07-22 11:18:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1212, Role: patient  
[2025-07-22 11:18:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1213, Role: patient  
[2025-07-22 11:18:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1214, Role: patient  
[2025-07-22 11:18:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1215, Role: patient  
[2025-07-22 11:18:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1216, Role: patient  
[2025-07-22 11:18:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1217, Role: patient  
[2025-07-22 11:18:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1218, Role: patient  
[2025-07-22 11:18:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1219, Role: patient  
[2025-07-22 11:18:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1220, Role: patient  
[2025-07-22 11:18:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1221, Role: patient  
[2025-07-22 11:18:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1222, Role: patient  
[2025-07-22 11:18:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1223, Role: patient  
[2025-07-22 11:18:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1224, Role: patient  
[2025-07-22 11:18:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1225, Role: patient  
[2025-07-22 11:18:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1226, Role: patient  
[2025-07-22 11:18:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1227, Role: patient  
[2025-07-22 11:18:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1228, Role: patient  
[2025-07-22 11:18:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1229, Role: patient  
[2025-07-22 11:18:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1230, Role: patient  
[2025-07-22 11:18:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1231, Role: patient  
[2025-07-22 11:18:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1232, Role: patient  
[2025-07-22 11:18:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1233, Role: patient  
[2025-07-22 11:18:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1234, Role: patient  
[2025-07-22 11:18:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1235, Role: patient  
[2025-07-22 11:18:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1236, Role: patient  
[2025-07-22 11:18:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1237, Role: patient  
[2025-07-22 11:18:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1238, Role: patient  
[2025-07-22 11:18:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1239, Role: patient  
[2025-07-22 11:18:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1240, Role: patient  
[2025-07-22 11:18:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1241, Role: patient  
[2025-07-22 11:18:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1242, Role: patient  
[2025-07-22 11:18:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1243, Role: patient  
[2025-07-22 11:18:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1244, Role: patient  
[2025-07-22 11:18:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1245, Role: patient  
[2025-07-22 11:18:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1246, Role: patient  
[2025-07-22 11:18:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1247, Role: patient  
[2025-07-22 11:18:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1248, Role: patient  
[2025-07-22 11:18:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1249, Role: patient  
[2025-07-22 11:18:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1250, Role: patient  
[2025-07-22 11:18:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1251, Role: patient  
[2025-07-22 11:18:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1252, Role: patient  
[2025-07-22 11:18:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1253, Role: patient  
[2025-07-22 11:18:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1254, Role: patient  
[2025-07-22 11:18:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1255, Role: patient  
[2025-07-22 11:18:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1256, Role: patient  
[2025-07-22 11:18:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1257, Role: patient  
[2025-07-22 11:18:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1258, Role: patient  
[2025-07-22 11:18:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1259, Role: patient  
[2025-07-22 11:18:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1260, Role: patient  
[2025-07-22 11:18:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1261, Role: patient  
[2025-07-22 11:18:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1262, Role: patient  
[2025-07-22 11:18:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1263, Role: patient  
[2025-07-22 11:18:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1264, Role: patient  
[2025-07-22 11:18:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1265, Role: patient  
[2025-07-22 11:18:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1266, Role: patient  
[2025-07-22 11:18:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1267, Role: patient  
[2025-07-22 11:18:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1268, Role: patient  
[2025-07-22 11:18:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1269, Role: patient  
[2025-07-22 11:18:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1270, Role: patient  
[2025-07-22 11:18:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1271, Role: patient  
[2025-07-22 11:18:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1272, Role: patient  
[2025-07-22 11:18:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1273, Role: patient  
[2025-07-22 11:18:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1274, Role: patient  
[2025-07-22 11:18:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1275, Role: patient  
[2025-07-22 11:18:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1276, Role: patient  
[2025-07-22 11:18:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1277, Role: patient  
[2025-07-22 11:18:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1278, Role: patient  
[2025-07-22 11:18:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1279, Role: patient  
[2025-07-22 11:18:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1280, Role: patient  
[2025-07-22 11:18:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1281, Role: patient  
[2025-07-22 11:18:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1282, Role: patient  
[2025-07-22 11:18:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1283, Role: patient  
[2025-07-22 11:18:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1284, Role: patient  
[2025-07-22 11:18:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1285, Role: patient  
[2025-07-22 11:18:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1286, Role: patient  
[2025-07-22 11:18:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1287, Role: patient  
[2025-07-22 11:18:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1288, Role: patient  
[2025-07-22 11:18:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1289, Role: patient  
[2025-07-22 11:18:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1290, Role: patient  
[2025-07-22 11:18:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1291, Role: patient  
[2025-07-22 11:18:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1292, Role: patient  
[2025-07-22 11:18:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1293, Role: patient  
[2025-07-22 11:18:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1294, Role: patient  
[2025-07-22 11:18:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1295, Role: patient  
[2025-07-22 11:18:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1296, Role: patient  
[2025-07-22 11:18:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1297, Role: patient  
[2025-07-22 11:18:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1298, Role: patient  
[2025-07-22 11:18:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1299, Role: patient  
[2025-07-22 11:18:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1300, Role: patient  
[2025-07-22 11:18:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1301, Role: patient  
[2025-07-22 11:18:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1302, Role: patient  
[2025-07-22 11:18:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1303, Role: patient  
[2025-07-22 11:18:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1304, Role: patient  
[2025-07-22 11:18:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1305, Role: patient  
[2025-07-22 11:18:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1306, Role: patient  
[2025-07-22 11:18:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1307, Role: patient  
[2025-07-22 11:18:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1308, Role: patient  
[2025-07-22 11:18:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1309, Role: patient  
[2025-07-22 11:18:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1310, Role: patient  
[2025-07-22 11:18:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1311, Role: patient  
[2025-07-22 11:18:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1312, Role: patient  
[2025-07-22 11:18:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1313, Role: patient  
[2025-07-22 11:18:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1314, Role: patient  
[2025-07-22 11:18:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1315, Role: patient  
[2025-07-22 11:18:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1316, Role: patient  
[2025-07-22 11:18:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1317, Role: patient  
[2025-07-22 11:18:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1318, Role: patient  
[2025-07-22 11:18:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1319, Role: patient  
[2025-07-22 11:18:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1320, Role: patient  
[2025-07-22 11:18:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1321, Role: patient  
[2025-07-22 11:18:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1322, Role: patient  
[2025-07-22 11:18:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1323, Role: patient  
[2025-07-22 11:18:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1324, Role: patient  
[2025-07-22 11:18:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1325, Role: patient  
[2025-07-22 11:18:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1326, Role: patient  
[2025-07-22 11:18:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1327, Role: patient  
[2025-07-22 11:18:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1328, Role: patient  
[2025-07-22 11:18:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1329, Role: patient  
[2025-07-22 11:18:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1330, Role: patient  
[2025-07-22 11:18:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1331, Role: patient  
[2025-07-22 11:18:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1332, Role: patient  
[2025-07-22 11:18:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1333, Role: patient  
[2025-07-22 11:18:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1334, Role: patient  
[2025-07-22 11:18:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1335, Role: patient  
[2025-07-22 11:18:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1336, Role: patient  
[2025-07-22 11:18:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1337, Role: patient  
[2025-07-22 11:18:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1338, Role: patient  
[2025-07-22 11:18:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1339, Role: patient  
[2025-07-22 11:18:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1340, Role: patient  
[2025-07-22 11:18:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1341, Role: patient  
[2025-07-22 11:18:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1342, Role: patient  
[2025-07-22 11:18:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1343, Role: patient  
[2025-07-22 11:18:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1344, Role: patient  
[2025-07-22 11:18:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1345, Role: patient  
[2025-07-22 11:18:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1346, Role: patient  
[2025-07-22 11:18:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1347, Role: patient  
[2025-07-22 11:18:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1348, Role: patient  
[2025-07-22 11:18:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1349, Role: patient  
[2025-07-22 11:18:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1350, Role: patient  
[2025-07-22 11:18:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1351, Role: patient  
[2025-07-22 11:18:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1352, Role: patient  
[2025-07-22 11:18:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1353, Role: patient  
[2025-07-22 11:18:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1354, Role: patient  
[2025-07-22 11:19:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1355, Role: patient  
[2025-07-22 11:19:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1356, Role: patient  
[2025-07-22 11:19:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1357, Role: patient  
[2025-07-22 11:19:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1358, Role: patient  
[2025-07-22 11:19:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1359, Role: patient  
[2025-07-22 11:19:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1360, Role: patient  
[2025-07-22 11:19:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1361, Role: patient  
[2025-07-22 11:19:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1362, Role: patient  
[2025-07-22 11:19:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1363, Role: patient  
[2025-07-22 11:19:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1364, Role: patient  
[2025-07-22 11:19:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1365, Role: patient  
[2025-07-22 11:19:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1366, Role: patient  
[2025-07-22 11:19:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1367, Role: patient  
[2025-07-22 11:19:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1368, Role: patient  
[2025-07-22 11:19:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1369, Role: patient  
[2025-07-22 11:19:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1370, Role: patient  
[2025-07-22 11:19:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1371, Role: patient  
[2025-07-22 11:19:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1372, Role: patient  
[2025-07-22 11:19:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1373, Role: patient  
[2025-07-22 11:19:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1374, Role: patient  
[2025-07-22 11:19:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1375, Role: patient  
[2025-07-22 11:19:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1376, Role: patient  
[2025-07-22 11:19:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1377, Role: patient  
[2025-07-22 11:19:09] local.INFO: Successfully migrated user peterclift123**@gmail.com → Laravel ID 1378, Role: patient  
[2025-07-22 11:19:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1379, Role: patient  
[2025-07-22 11:19:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1380, Role: patient  
[2025-07-22 11:19:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1381, Role: patient  
[2025-07-22 11:19:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1382, Role: patient  
[2025-07-22 11:19:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1383, Role: patient  
[2025-07-22 11:19:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1384, Role: patient  
[2025-07-22 11:19:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1385, Role: patient  
[2025-07-22 11:19:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1386, Role: patient  
[2025-07-22 11:19:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1387, Role: patient  
[2025-07-22 11:19:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1388, Role: patient  
[2025-07-22 11:19:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1389, Role: patient  
[2025-07-22 11:19:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1390, Role: patient  
[2025-07-22 11:19:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1391, Role: patient  
[2025-07-22 11:19:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1392, Role: patient  
[2025-07-22 11:19:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1393, Role: patient  
[2025-07-22 11:19:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1394, Role: patient  
[2025-07-22 11:19:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1395, Role: patient  
[2025-07-22 11:19:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1396, Role: patient  
[2025-07-22 11:19:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1397, Role: patient  
[2025-07-22 11:19:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1398, Role: patient  
[2025-07-22 11:19:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1399, Role: patient  
[2025-07-22 11:19:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1400, Role: patient  
[2025-07-22 11:19:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1401, Role: patient  
[2025-07-22 11:19:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1402, Role: patient  
[2025-07-22 11:19:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1403, Role: patient  
[2025-07-22 11:19:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1404, Role: patient  
[2025-07-22 11:19:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1405, Role: patient  
[2025-07-22 11:19:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1406, Role: patient  
[2025-07-22 11:19:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1407, Role: patient  
[2025-07-22 11:19:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1408, Role: patient  
[2025-07-22 11:19:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1409, Role: patient  
[2025-07-22 11:19:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1410, Role: patient  
[2025-07-22 11:19:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1411, Role: patient  
[2025-07-22 11:19:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1412, Role: patient  
[2025-07-22 11:19:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1413, Role: patient  
[2025-07-22 11:19:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1414, Role: patient  
[2025-07-22 11:19:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1415, Role: patient  
[2025-07-22 11:19:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1416, Role: patient  
[2025-07-22 11:19:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1417, Role: patient  
[2025-07-22 11:19:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1418, Role: patient  
[2025-07-22 11:19:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1419, Role: patient  
[2025-07-22 11:19:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1420, Role: patient  
[2025-07-22 11:19:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1421, Role: patient  
[2025-07-22 11:19:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1422, Role: patient  
[2025-07-22 11:19:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1423, Role: patient  
[2025-07-22 11:19:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1424, Role: patient  
[2025-07-22 11:19:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1425, Role: patient  
[2025-07-22 11:19:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1426, Role: patient  
[2025-07-22 11:19:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1427, Role: patient  
[2025-07-22 11:19:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1428, Role: patient  
[2025-07-22 11:19:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1429, Role: patient  
[2025-07-22 11:19:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1430, Role: patient  
[2025-07-22 11:19:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1431, Role: patient  
[2025-07-22 11:19:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1432, Role: patient  
[2025-07-22 11:19:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1433, Role: patient  
[2025-07-22 11:19:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1434, Role: patient  
[2025-07-22 11:19:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1435, Role: patient  
[2025-07-22 11:19:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1436, Role: patient  
[2025-07-22 11:19:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1437, Role: patient  
[2025-07-22 11:19:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1438, Role: patient  
[2025-07-22 11:19:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1439, Role: patient  
[2025-07-22 11:19:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1440, Role: patient  
[2025-07-22 11:19:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1441, Role: patient  
[2025-07-22 11:19:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1442, Role: patient  
[2025-07-22 11:19:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1443, Role: patient  
[2025-07-22 11:19:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1444, Role: patient  
[2025-07-22 11:19:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1445, Role: patient  
[2025-07-22 11:19:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1446, Role: patient  
[2025-07-22 11:19:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1447, Role: patient  
[2025-07-22 11:19:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1448, Role: patient  
[2025-07-22 11:19:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1449, Role: patient  
[2025-07-22 11:19:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1450, Role: patient  
[2025-07-22 11:19:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1451, Role: patient  
[2025-07-22 11:19:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1452, Role: patient  
[2025-07-22 11:19:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1453, Role: patient  
[2025-07-22 11:19:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1454, Role: patient  
[2025-07-22 11:19:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1455, Role: patient  
[2025-07-22 11:19:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1456, Role: patient  
[2025-07-22 11:19:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1457, Role: patient  
[2025-07-22 11:19:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1458, Role: patient  
[2025-07-22 11:19:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1459, Role: patient  
[2025-07-22 11:19:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1460, Role: patient  
[2025-07-22 11:19:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1461, Role: patient  
[2025-07-22 11:19:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1462, Role: patient  
[2025-07-22 11:19:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1463, Role: patient  
[2025-07-22 11:19:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1464, Role: patient  
[2025-07-22 11:19:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1465, Role: patient  
[2025-07-22 11:19:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1466, Role: patient  
[2025-07-22 11:19:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1467, Role: patient  
[2025-07-22 11:19:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1468, Role: patient  
[2025-07-22 11:19:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1469, Role: patient  
[2025-07-22 11:19:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1470, Role: patient  
[2025-07-22 11:19:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1471, Role: patient  
[2025-07-22 11:19:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1472, Role: patient  
[2025-07-22 11:19:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1473, Role: patient  
[2025-07-22 11:19:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1474, Role: patient  
[2025-07-22 11:19:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1475, Role: patient  
[2025-07-22 11:19:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1476, Role: patient  
[2025-07-22 11:19:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1477, Role: patient  
[2025-07-22 11:19:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1478, Role: patient  
[2025-07-22 11:19:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1479, Role: patient  
[2025-07-22 11:19:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1480, Role: patient  
[2025-07-22 11:19:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1481, Role: patient  
[2025-07-22 11:19:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1482, Role: patient  
[2025-07-22 11:19:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1483, Role: patient  
[2025-07-22 11:19:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1484, Role: patient  
[2025-07-22 11:19:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1485, Role: patient  
[2025-07-22 11:19:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1486, Role: patient  
[2025-07-22 11:19:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1487, Role: patient  
[2025-07-22 11:19:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1488, Role: patient  
[2025-07-22 11:19:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1489, Role: patient  
[2025-07-22 11:19:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1490, Role: patient  
[2025-07-22 11:19:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1491, Role: patient  
[2025-07-22 11:19:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1492, Role: patient  
[2025-07-22 11:19:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1493, Role: patient  
[2025-07-22 11:19:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1494, Role: patient  
[2025-07-22 11:19:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1495, Role: patient  
[2025-07-22 11:19:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1496, Role: patient  
[2025-07-22 11:19:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1497, Role: patient  
[2025-07-22 11:19:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1498, Role: patient  
[2025-07-22 11:19:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1499, Role: patient  
[2025-07-22 11:19:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1500, Role: patient  
[2025-07-22 11:19:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1501, Role: staff  
[2025-07-22 11:19:55] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:19:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1502, Role: clinic_admin  
[2025-07-22 11:19:55] local.INFO: Clinic 14 completed: 524 processed, 0 skipped, 0 errors  
[2025-07-22 11:19:55] local.INFO: Starting users migration for clinic 13  
[2025-07-22 11:19:55] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:19:59] local.INFO: Found 2 users for clinic 13  
[2025-07-22 11:19:59] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:19:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1503, Role: provider  
[2025-07-22 11:19:59] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:19:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1504, Role: clinic_admin  
[2025-07-22 11:19:59] local.INFO: Clinic 13 completed: 2 processed, 0 skipped, 0 errors  
[2025-07-22 11:19:59] local.INFO: Starting users migration for clinic 12  
[2025-07-22 11:19:59] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:20:03] local.INFO: Found 36 users for clinic 12  
[2025-07-22 11:20:03] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:20:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1505, Role: provider  
[2025-07-22 11:20:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1506, Role: patient  
[2025-07-22 11:20:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1507, Role: patient  
[2025-07-22 11:20:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1508, Role: patient  
[2025-07-22 11:20:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1509, Role: patient  
[2025-07-22 11:20:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1510, Role: patient  
[2025-07-22 11:20:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1511, Role: patient  
[2025-07-22 11:20:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1512, Role: patient  
[2025-07-22 11:20:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1513, Role: patient  
[2025-07-22 11:20:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1514, Role: patient  
[2025-07-22 11:20:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1515, Role: patient  
[2025-07-22 11:20:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1516, Role: patient  
[2025-07-22 11:20:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1517, Role: patient  
[2025-07-22 11:20:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1518, Role: patient  
[2025-07-22 11:20:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1519, Role: patient  
[2025-07-22 11:20:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1520, Role: patient  
[2025-07-22 11:20:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1521, Role: patient  
[2025-07-22 11:20:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1522, Role: patient  
[2025-07-22 11:20:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1523, Role: patient  
[2025-07-22 11:20:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1524, Role: patient  
[2025-07-22 11:20:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1525, Role: patient  
[2025-07-22 11:20:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1526, Role: patient  
[2025-07-22 11:20:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1527, Role: patient  
[2025-07-22 11:20:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1528, Role: patient  
[2025-07-22 11:20:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1529, Role: patient  
[2025-07-22 11:20:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1530, Role: patient  
[2025-07-22 11:20:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1531, Role: patient  
[2025-07-22 11:20:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1532, Role: patient  
[2025-07-22 11:20:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1533, Role: patient  
[2025-07-22 11:20:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1534, Role: patient  
[2025-07-22 11:20:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1535, Role: patient  
[2025-07-22 11:20:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1536, Role: patient  
[2025-07-22 11:20:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1537, Role: patient  
[2025-07-22 11:20:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1538, Role: patient  
[2025-07-22 11:20:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1539, Role: patient  
[2025-07-22 11:20:15] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:20:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1540, Role: clinic_admin  
[2025-07-22 11:20:15] local.INFO: Clinic 12 completed: 36 processed, 0 skipped, 0 errors  
[2025-07-22 11:20:15] local.INFO: Starting users migration for clinic 11  
[2025-07-22 11:20:15] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:20:20] local.INFO: Found 276 users for clinic 11  
[2025-07-22 11:20:21] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:20:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1541, Role: provider  
[2025-07-22 11:20:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1542, Role: patient  
[2025-07-22 11:20:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1543, Role: patient  
[2025-07-22 11:20:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1544, Role: patient  
[2025-07-22 11:20:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1545, Role: patient  
[2025-07-22 11:20:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1546, Role: patient  
[2025-07-22 11:20:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1547, Role: patient  
[2025-07-22 11:20:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1548, Role: patient  
[2025-07-22 11:20:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1549, Role: patient  
[2025-07-22 11:20:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1550, Role: patient  
[2025-07-22 11:20:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1551, Role: patient  
[2025-07-22 11:20:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1552, Role: patient  
[2025-07-22 11:20:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1553, Role: patient  
[2025-07-22 11:20:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1554, Role: patient  
[2025-07-22 11:20:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1555, Role: patient  
[2025-07-22 11:20:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1556, Role: patient  
[2025-07-22 11:20:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1557, Role: patient  
[2025-07-22 11:20:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1558, Role: patient  
[2025-07-22 11:20:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1559, Role: patient  
[2025-07-22 11:20:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1560, Role: patient  
[2025-07-22 11:20:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1561, Role: patient  
[2025-07-22 11:20:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1562, Role: patient  
[2025-07-22 11:20:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1563, Role: patient  
[2025-07-22 11:20:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1564, Role: patient  
[2025-07-22 11:20:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1565, Role: patient  
[2025-07-22 11:20:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1566, Role: patient  
[2025-07-22 11:20:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1567, Role: patient  
[2025-07-22 11:20:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1568, Role: patient  
[2025-07-22 11:20:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1569, Role: patient  
[2025-07-22 11:20:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1570, Role: patient  
[2025-07-22 11:20:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1571, Role: patient  
[2025-07-22 11:20:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1572, Role: patient  
[2025-07-22 11:20:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1573, Role: patient  
[2025-07-22 11:20:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1574, Role: patient  
[2025-07-22 11:20:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1575, Role: patient  
[2025-07-22 11:20:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1576, Role: patient  
[2025-07-22 11:20:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1577, Role: patient  
[2025-07-22 11:20:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1578, Role: patient  
[2025-07-22 11:20:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1579, Role: patient  
[2025-07-22 11:20:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1580, Role: patient  
[2025-07-22 11:20:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1581, Role: patient  
[2025-07-22 11:20:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1582, Role: patient  
[2025-07-22 11:20:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1583, Role: patient  
[2025-07-22 11:20:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1584, Role: patient  
[2025-07-22 11:20:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1585, Role: patient  
[2025-07-22 11:20:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1586, Role: patient  
[2025-07-22 11:20:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1587, Role: patient  
[2025-07-22 11:20:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1588, Role: patient  
[2025-07-22 11:20:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1589, Role: patient  
[2025-07-22 11:20:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1590, Role: patient  
[2025-07-22 11:20:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1591, Role: patient  
[2025-07-22 11:20:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1592, Role: patient  
[2025-07-22 11:20:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1593, Role: patient  
[2025-07-22 11:20:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1594, Role: patient  
[2025-07-22 11:20:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1595, Role: patient  
[2025-07-22 11:20:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1596, Role: patient  
[2025-07-22 11:20:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1597, Role: patient  
[2025-07-22 11:20:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1598, Role: patient  
[2025-07-22 11:20:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1599, Role: patient  
[2025-07-22 11:20:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1600, Role: patient  
[2025-07-22 11:20:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1601, Role: patient  
[2025-07-22 11:20:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1602, Role: patient  
[2025-07-22 11:20:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1603, Role: patient  
[2025-07-22 11:20:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1604, Role: patient  
[2025-07-22 11:20:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1605, Role: patient  
[2025-07-22 11:20:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1606, Role: patient  
[2025-07-22 11:20:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1607, Role: patient  
[2025-07-22 11:20:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1608, Role: patient  
[2025-07-22 11:20:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1609, Role: patient  
[2025-07-22 11:20:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1610, Role: patient  
[2025-07-22 11:20:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1611, Role: patient  
[2025-07-22 11:20:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1612, Role: patient  
[2025-07-22 11:20:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1613, Role: patient  
[2025-07-22 11:20:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1614, Role: patient  
[2025-07-22 11:20:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1615, Role: patient  
[2025-07-22 11:20:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1616, Role: patient  
[2025-07-22 11:20:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1617, Role: patient  
[2025-07-22 11:20:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1618, Role: patient  
[2025-07-22 11:20:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1619, Role: patient  
[2025-07-22 11:20:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1620, Role: patient  
[2025-07-22 11:20:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1621, Role: patient  
[2025-07-22 11:20:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1622, Role: patient  
[2025-07-22 11:20:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1623, Role: patient  
[2025-07-22 11:20:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1624, Role: patient  
[2025-07-22 11:20:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1625, Role: patient  
[2025-07-22 11:20:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1626, Role: patient  
[2025-07-22 11:20:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1627, Role: patient  
[2025-07-22 11:20:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1628, Role: patient  
[2025-07-22 11:20:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1629, Role: patient  
[2025-07-22 11:20:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1630, Role: patient  
[2025-07-22 11:20:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1631, Role: patient  
[2025-07-22 11:20:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1632, Role: patient  
[2025-07-22 11:20:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1633, Role: patient  
[2025-07-22 11:20:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1634, Role: patient  
[2025-07-22 11:20:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1635, Role: patient  
[2025-07-22 11:20:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1636, Role: patient  
[2025-07-22 11:20:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1637, Role: patient  
[2025-07-22 11:20:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1638, Role: patient  
[2025-07-22 11:20:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1639, Role: patient  
[2025-07-22 11:20:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1640, Role: patient  
[2025-07-22 11:20:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1641, Role: patient  
[2025-07-22 11:20:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1642, Role: patient  
[2025-07-22 11:20:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1643, Role: patient  
[2025-07-22 11:20:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1644, Role: patient  
[2025-07-22 11:20:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1645, Role: patient  
[2025-07-22 11:20:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1646, Role: patient  
[2025-07-22 11:20:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1647, Role: patient  
[2025-07-22 11:20:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1648, Role: patient  
[2025-07-22 11:21:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1649, Role: patient  
[2025-07-22 11:21:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1650, Role: patient  
[2025-07-22 11:21:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1651, Role: patient  
[2025-07-22 11:21:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1652, Role: patient  
[2025-07-22 11:21:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1653, Role: patient  
[2025-07-22 11:21:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1654, Role: patient  
[2025-07-22 11:21:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1655, Role: patient  
[2025-07-22 11:21:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1656, Role: patient  
[2025-07-22 11:21:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1657, Role: patient  
[2025-07-22 11:21:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1658, Role: patient  
[2025-07-22 11:21:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1659, Role: patient  
[2025-07-22 11:21:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1660, Role: patient  
[2025-07-22 11:21:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1661, Role: patient  
[2025-07-22 11:21:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1662, Role: patient  
[2025-07-22 11:21:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1663, Role: patient  
[2025-07-22 11:21:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1664, Role: patient  
[2025-07-22 11:21:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1665, Role: patient  
[2025-07-22 11:21:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1666, Role: patient  
[2025-07-22 11:21:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1667, Role: patient  
[2025-07-22 11:21:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1668, Role: patient  
[2025-07-22 11:21:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1669, Role: patient  
[2025-07-22 11:21:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1670, Role: patient  
[2025-07-22 11:21:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1671, Role: patient  
[2025-07-22 11:21:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1672, Role: patient  
[2025-07-22 11:21:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1673, Role: patient  
[2025-07-22 11:21:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1674, Role: patient  
[2025-07-22 11:21:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1675, Role: patient  
[2025-07-22 11:21:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1676, Role: patient  
[2025-07-22 11:21:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1677, Role: patient  
[2025-07-22 11:21:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1678, Role: patient  
[2025-07-22 11:21:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1679, Role: patient  
[2025-07-22 11:21:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1680, Role: patient  
[2025-07-22 11:21:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1681, Role: patient  
[2025-07-22 11:21:11] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1682, Role: patient  
[2025-07-22 11:21:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1683, Role: patient  
[2025-07-22 11:21:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1684, Role: patient  
[2025-07-22 11:21:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1685, Role: patient  
[2025-07-22 11:21:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1686, Role: patient  
[2025-07-22 11:21:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1687, Role: patient  
[2025-07-22 11:21:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1688, Role: patient  
[2025-07-22 11:21:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1689, Role: patient  
[2025-07-22 11:21:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1690, Role: patient  
[2025-07-22 11:21:15] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1691, Role: patient  
[2025-07-22 11:21:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1692, Role: patient  
[2025-07-22 11:21:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1693, Role: patient  
[2025-07-22 11:21:16] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1694, Role: patient  
[2025-07-22 11:21:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1695, Role: patient  
[2025-07-22 11:21:17] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1696, Role: patient  
[2025-07-22 11:21:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1697, Role: patient  
[2025-07-22 11:21:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1698, Role: patient  
[2025-07-22 11:21:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1699, Role: patient  
[2025-07-22 11:21:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1700, Role: patient  
[2025-07-22 11:21:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1701, Role: patient  
[2025-07-22 11:21:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1702, Role: patient  
[2025-07-22 11:21:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1703, Role: patient  
[2025-07-22 11:21:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1704, Role: patient  
[2025-07-22 11:21:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1705, Role: patient  
[2025-07-22 11:21:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1706, Role: patient  
[2025-07-22 11:21:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1707, Role: patient  
[2025-07-22 11:21:22] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1708, Role: patient  
[2025-07-22 11:21:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1709, Role: patient  
[2025-07-22 11:21:23] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1710, Role: patient  
[2025-07-22 11:21:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1711, Role: patient  
[2025-07-22 11:21:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1712, Role: patient  
[2025-07-22 11:21:24] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1713, Role: patient  
[2025-07-22 11:21:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1714, Role: patient  
[2025-07-22 11:21:25] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1715, Role: patient  
[2025-07-22 11:21:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1716, Role: patient  
[2025-07-22 11:21:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1717, Role: patient  
[2025-07-22 11:21:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1718, Role: patient  
[2025-07-22 11:21:27] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1719, Role: patient  
[2025-07-22 11:21:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1720, Role: patient  
[2025-07-22 11:21:28] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1721, Role: patient  
[2025-07-22 11:21:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1722, Role: patient  
[2025-07-22 11:21:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1723, Role: patient  
[2025-07-22 11:21:29] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1724, Role: patient  
[2025-07-22 11:21:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1725, Role: patient  
[2025-07-22 11:21:30] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1726, Role: patient  
[2025-07-22 11:21:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1727, Role: patient  
[2025-07-22 11:21:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1728, Role: patient  
[2025-07-22 11:21:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1729, Role: patient  
[2025-07-22 11:21:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1730, Role: patient  
[2025-07-22 11:21:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1731, Role: patient  
[2025-07-22 11:21:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1732, Role: patient  
[2025-07-22 11:21:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1733, Role: patient  
[2025-07-22 11:21:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1734, Role: patient  
[2025-07-22 11:21:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1735, Role: patient  
[2025-07-22 11:21:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1736, Role: patient  
[2025-07-22 11:21:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1737, Role: patient  
[2025-07-22 11:21:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1738, Role: patient  
[2025-07-22 11:21:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1739, Role: patient  
[2025-07-22 11:21:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1740, Role: patient  
[2025-07-22 11:21:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1741, Role: patient  
[2025-07-22 11:21:36] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1742, Role: patient  
[2025-07-22 11:21:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1743, Role: patient  
[2025-07-22 11:21:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1744, Role: patient  
[2025-07-22 11:21:37] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1745, Role: patient  
[2025-07-22 11:21:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1746, Role: patient  
[2025-07-22 11:21:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1747, Role: patient  
[2025-07-22 11:21:38] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1748, Role: patient  
[2025-07-22 11:21:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1749, Role: patient  
[2025-07-22 11:21:39] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1750, Role: patient  
[2025-07-22 11:21:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1751, Role: patient  
[2025-07-22 11:21:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1752, Role: patient  
[2025-07-22 11:21:40] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1753, Role: patient  
[2025-07-22 11:21:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1754, Role: patient  
[2025-07-22 11:21:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1755, Role: patient  
[2025-07-22 11:21:41] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1756, Role: patient  
[2025-07-22 11:21:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1757, Role: patient  
[2025-07-22 11:21:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1758, Role: patient  
[2025-07-22 11:21:42] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1759, Role: patient  
[2025-07-22 11:21:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1760, Role: patient  
[2025-07-22 11:21:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1761, Role: patient  
[2025-07-22 11:21:43] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1762, Role: patient  
[2025-07-22 11:21:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1763, Role: patient  
[2025-07-22 11:21:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1764, Role: patient  
[2025-07-22 11:21:44] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1765, Role: patient  
[2025-07-22 11:21:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1766, Role: patient  
[2025-07-22 11:21:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1767, Role: patient  
[2025-07-22 11:21:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1768, Role: patient  
[2025-07-22 11:21:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1769, Role: patient  
[2025-07-22 11:21:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1770, Role: patient  
[2025-07-22 11:21:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1771, Role: patient  
[2025-07-22 11:21:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1772, Role: patient  
[2025-07-22 11:21:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1773, Role: patient  
[2025-07-22 11:21:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1774, Role: patient  
[2025-07-22 11:21:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1775, Role: patient  
[2025-07-22 11:21:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1776, Role: patient  
[2025-07-22 11:21:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1777, Role: patient  
[2025-07-22 11:21:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1778, Role: patient  
[2025-07-22 11:21:49] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1779, Role: patient  
[2025-07-22 11:21:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1780, Role: patient  
[2025-07-22 11:21:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1781, Role: patient  
[2025-07-22 11:21:50] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1782, Role: patient  
[2025-07-22 11:21:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1783, Role: patient  
[2025-07-22 11:21:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1784, Role: patient  
[2025-07-22 11:21:51] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1785, Role: patient  
[2025-07-22 11:21:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1786, Role: patient  
[2025-07-22 11:21:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1787, Role: patient  
[2025-07-22 11:21:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1788, Role: patient  
[2025-07-22 11:21:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1789, Role: patient  
[2025-07-22 11:21:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1790, Role: patient  
[2025-07-22 11:21:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1791, Role: patient  
[2025-07-22 11:21:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1792, Role: patient  
[2025-07-22 11:21:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1793, Role: patient  
[2025-07-22 11:21:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1794, Role: patient  
[2025-07-22 11:21:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1795, Role: patient  
[2025-07-22 11:21:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1796, Role: patient  
[2025-07-22 11:21:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1797, Role: patient  
[2025-07-22 11:21:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1798, Role: patient  
[2025-07-22 11:21:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1799, Role: patient  
[2025-07-22 11:21:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1800, Role: patient  
[2025-07-22 11:21:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1801, Role: patient  
[2025-07-22 11:21:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1802, Role: patient  
[2025-07-22 11:21:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1803, Role: patient  
[2025-07-22 11:21:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1804, Role: patient  
[2025-07-22 11:21:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1805, Role: patient  
[2025-07-22 11:21:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1806, Role: patient  
[2025-07-22 11:21:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1807, Role: patient  
[2025-07-22 11:22:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1808, Role: patient  
[2025-07-22 11:22:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1809, Role: patient  
[2025-07-22 11:22:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1810, Role: patient  
[2025-07-22 11:22:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1811, Role: patient  
[2025-07-22 11:22:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1812, Role: patient  
[2025-07-22 11:22:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1813, Role: patient  
[2025-07-22 11:22:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1814, Role: patient  
[2025-07-22 11:22:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1815, Role: patient  
[2025-07-22 11:22:02] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1816, Role: clinic_admin  
[2025-07-22 11:22:02] local.INFO: Clinic 11 completed: 276 processed, 0 skipped, 0 errors  
[2025-07-22 11:22:02] local.INFO: Starting users migration for clinic 10  
[2025-07-22 11:22:02] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:22:08] local.INFO: Found 3 users for clinic 10  
[2025-07-22 11:22:08] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1817, Role: provider  
[2025-07-22 11:22:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1818, Role: patient  
[2025-07-22 11:22:09] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1819, Role: clinic_admin  
[2025-07-22 11:22:09] local.INFO: Clinic 10 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 11:22:09] local.INFO: Starting users migration for clinic 9  
[2025-07-22 11:22:09] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:22:12] local.INFO: Found 6 users for clinic 9  
[2025-07-22 11:22:12] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:12] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1820, Role: provider  
[2025-07-22 11:22:13] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1821, Role: provider  
[2025-07-22 11:22:13] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1822, Role: provider  
[2025-07-22 11:22:13] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:13] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1823, Role: provider  
[2025-07-22 11:22:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1824, Role: patient  
[2025-07-22 11:22:14] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:14] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1825, Role: clinic_admin  
[2025-07-22 11:22:14] local.INFO: Clinic 9 completed: 6 processed, 0 skipped, 0 errors  
[2025-07-22 11:22:14] local.INFO: Starting users migration for clinic 8  
[2025-07-22 11:22:14] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:22:17] local.INFO: Found 11 users for clinic 8  
[2025-07-22 11:22:18] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1826, Role: provider  
[2025-07-22 11:22:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1827, Role: patient  
[2025-07-22 11:22:18] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1828, Role: patient  
[2025-07-22 11:22:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1829, Role: patient  
[2025-07-22 11:22:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1830, Role: patient  
[2025-07-22 11:22:19] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1831, Role: patient  
[2025-07-22 11:22:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1832, Role: patient  
[2025-07-22 11:22:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1833, Role: patient  
[2025-07-22 11:22:20] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1834, Role: patient  
[2025-07-22 11:22:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1835, Role: patient  
[2025-07-22 11:22:21] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:21] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1836, Role: clinic_admin  
[2025-07-22 11:22:21] local.INFO: Clinic 8 completed: 11 processed, 0 skipped, 0 errors  
[2025-07-22 11:22:21] local.INFO: Starting users migration for clinic 6  
[2025-07-22 11:22:21] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:22:25] local.INFO: Found 3 users for clinic 6  
[2025-07-22 11:22:26] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1837, Role: provider  
[2025-07-22 11:22:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1838, Role: patient  
[2025-07-22 11:22:26] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:26] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1839, Role: clinic_admin  
[2025-07-22 11:22:26] local.INFO: Clinic 6 completed: 3 processed, 0 skipped, 0 errors  
[2025-07-22 11:22:26] local.INFO: Starting users migration for clinic 5  
[2025-07-22 11:22:26] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:22:31] local.INFO: Found 16 users for clinic 5  
[2025-07-22 11:22:31] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1840, Role: provider  
[2025-07-22 11:22:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1841, Role: patient  
[2025-07-22 11:22:31] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1842, Role: patient  
[2025-07-22 11:22:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1843, Role: patient  
[2025-07-22 11:22:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1844, Role: patient  
[2025-07-22 11:22:32] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1845, Role: patient  
[2025-07-22 11:22:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1846, Role: patient  
[2025-07-22 11:22:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1847, Role: patient  
[2025-07-22 11:22:33] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1848, Role: patient  
[2025-07-22 11:22:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1849, Role: patient  
[2025-07-22 11:22:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1850, Role: patient  
[2025-07-22 11:22:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1851, Role: patient  
[2025-07-22 11:22:34] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1852, Role: patient  
[2025-07-22 11:22:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1853, Role: patient  
[2025-07-22 11:22:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1854, Role: patient  
[2025-07-22 11:22:35] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:35] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1855, Role: clinic_admin  
[2025-07-22 11:22:35] local.INFO: Clinic 5 completed: 16 processed, 0 skipped, 0 errors  
[2025-07-22 11:22:35] local.INFO: Starting users migration for clinic 4  
[2025-07-22 11:22:35] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:22:40] local.INFO: No users found for clinic 4  
[2025-07-22 11:22:40] local.INFO: Starting users migration for clinic 2  
[2025-07-22 11:22:40] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:22:45] local.INFO: Found 7 users for clinic 2  
[2025-07-22 11:22:45] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:45] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1856, Role: provider  
[2025-07-22 11:22:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1857, Role: patient  
[2025-07-22 11:22:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1858, Role: patient  
[2025-07-22 11:22:46] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1859, Role: patient  
[2025-07-22 11:22:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1860, Role: patient  
[2025-07-22 11:22:47] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1861, Role: patient  
[2025-07-22 11:22:48] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:48] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1862, Role: clinic_admin  
[2025-07-22 11:22:48] local.INFO: Clinic 2 completed: 7 processed, 0 skipped, 0 errors  
[2025-07-22 11:22:48] local.INFO: Starting users migration for clinic 1  
[2025-07-22 11:22:48] local.INFO: Making KiviCare API request: laravel_get_clinic_users  
[2025-07-22 11:22:52] local.INFO: Found 54 users for clinic 1  
[2025-07-22 11:22:52] local.INFO: <NAME_EMAIL> - Email already exists  
[2025-07-22 11:22:52] local.INFO: Created/updated provider record <NAME_EMAIL>  
[2025-07-22 11:22:52] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1863, Role: provider  
[2025-07-22 11:22:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1864, Role: patient  
[2025-07-22 11:22:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1865, Role: patient  
[2025-07-22 11:22:53] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1866, Role: patient  
[2025-07-22 11:22:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1867, Role: patient  
[2025-07-22 11:22:54] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1868, Role: patient  
[2025-07-22 11:22:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1869, Role: patient  
[2025-07-22 11:22:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1870, Role: patient  
[2025-07-22 11:22:55] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1871, Role: patient  
[2025-07-22 11:22:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1872, Role: patient  
[2025-07-22 11:22:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1873, Role: patient  
[2025-07-22 11:22:56] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1874, Role: patient  
[2025-07-22 11:22:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1875, Role: patient  
[2025-07-22 11:22:57] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1876, Role: patient  
[2025-07-22 11:22:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1877, Role: patient  
[2025-07-22 11:22:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1878, Role: patient  
[2025-07-22 11:22:58] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1879, Role: patient  
[2025-07-22 11:22:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1880, Role: patient  
[2025-07-22 11:22:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1881, Role: patient  
[2025-07-22 11:22:59] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1882, Role: patient  
[2025-07-22 11:23:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1883, Role: patient  
[2025-07-22 11:23:00] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1884, Role: patient  
[2025-07-22 11:23:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1885, Role: patient  
[2025-07-22 11:23:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1886, Role: patient  
[2025-07-22 11:23:01] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1887, Role: patient  
[2025-07-22 11:23:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1888, Role: patient  
[2025-07-22 11:23:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1889, Role: patient  
[2025-07-22 11:23:02] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1890, Role: patient  
[2025-07-22 11:23:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1891, Role: patient  
[2025-07-22 11:23:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1892, Role: patient  
[2025-07-22 11:23:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1893, Role: patient  
[2025-07-22 11:23:03] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1894, Role: patient  
[2025-07-22 11:23:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1895, Role: patient  
[2025-07-22 11:23:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1896, Role: patient  
[2025-07-22 11:23:04] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1897, Role: patient  
[2025-07-22 11:23:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1898, Role: patient  
[2025-07-22 11:23:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1899, Role: patient  
[2025-07-22 11:23:05] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1900, Role: patient  
[2025-07-22 11:23:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1901, Role: patient  
[2025-07-22 11:23:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1902, Role: patient  
[2025-07-22 11:23:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1903, Role: patient  
[2025-07-22 11:23:06] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1904, Role: patient  
[2025-07-22 11:23:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1905, Role: patient  
[2025-07-22 11:23:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1906, Role: patient  
[2025-07-22 11:23:07] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1907, Role: patient  
[2025-07-22 11:23:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1908, Role: patient  
[2025-07-22 11:23:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1909, Role: patient  
[2025-07-22 11:23:08] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1910, Role: patient  
[2025-07-22 11:23:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1911, Role: patient  
[2025-07-22 11:23:09] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1912, Role: patient  
[2025-07-22 11:23:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1913, Role: patient  
[2025-07-22 11:23:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1914, Role: patient  
[2025-07-22 11:23:10] local.INFO: Successfully <NAME_EMAIL> → Laravel ID 1915, Role: patient  
[2025-07-22 11:23:10] local.INFO: Clinic 1 completed: 53 processed, 1 skipped, 0 errors  
[2025-07-22 11:23:10] local.INFO: Migration completed - Processed: 957, Skipped: 1, Errors: 0  
[2025-07-22 11:23:10] local.INFO: Migration command completed successfully  
