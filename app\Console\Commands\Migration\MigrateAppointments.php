<?php

namespace App\Console\Commands\Migration;

use App\Models\Appointment;
use App\Models\User;
use App\Models\Patient;
use App\Models\Provider;
use App\Services\Migration\DataTransformer;
use Exception;
use Illuminate\Support\Facades\Log;

/**
 * Migrate Appointments Command
 * 
 * Simple focused migration for appointments only
 */
class MigrateAppointments extends BaseMigrationCommand
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'migratewp:appointments
                            {--clinic= : Specific clinic ID, comma-separated IDs, or "all" for all clinics}
                            {--dry-run : Preview what would be migrated without executing}
                            {--force : Skip confirmation prompts}';

    /**
     * The console command description.
     */
    protected $description = 'Migrate appointments from WordPress to Laravel';

    /**
     * Data transformer instance
     */
    protected $transformer;

    /**
     * Log channels
     */
    protected $logChannel;
    protected $errorLogChannel;

    public function __construct()
    {
        parent::__construct();
        $this->transformer = new DataTransformer();
        $this->setupLogging();
    }

    /**
     * Setup dedicated logging for appointments migration
     */
    protected function setupLogging()
    {
        $date = now()->format('Y_m_d');
        $logPath = storage_path("logs/migration/appointments");
        
        // Create directory if it doesn't exist
        if (!file_exists($logPath)) {
            mkdir($logPath, 0755, true);
        }

        // Configure log channels
        config([
            'logging.channels.appointments_migration' => [
                'driver' => 'single',
                'path' => $logPath . "/appointments_{$date}.log",
                'level' => 'info',
            ],
            'logging.channels.appointments_errors' => [
                'driver' => 'single',
                'path' => $logPath . "/appointments_errors_{$date}.log",
                'level' => 'error',
            ]
        ]);

        $this->logChannel = 'appointments_migration';
        $this->errorLogChannel = 'appointments_errors';
    }

    /**
     * Execute the command
     */
    protected function executeCommand()
    {
        $this->info("=== MIGRATING APPOINTMENTS ===");
        $this->logInfo("Starting appointments migration");
        
        if ($this->isDryRun()) {
            $this->warn("DRY RUN MODE - Previewing migration without making changes");
            $this->logInfo("Running in DRY RUN mode");
        }

        $clinicOption = $this->option('clinic') ?? 'all';
        $clinicIds = $this->parseClinicOption($clinicOption);

        $totalProcessed = 0;
        $totalSkipped = 0;
        $totalErrors = 0;
        $failedClinics = [];

        foreach ($clinicIds as $clinicId) {
            $this->info("\n--- Migrating appointments for clinic ID: {$clinicId} ---");
            $this->logInfo("Starting appointments migration for clinic {$clinicId}");
            
            try {
                // 1. Make API call - get appointments for this clinic
                $response = $this->makeApiRequest('laravel_get_clinic_appointments', ['clinic_id' => $clinicId]);
                $wpAppointments = $response['data'] ?? [];

                if (empty($wpAppointments)) {
                    $this->info("No appointments found for clinic {$clinicId}");
                    $this->logInfo("No appointments found for clinic {$clinicId}");
                    continue;
                }

                $this->info("Found " . count($wpAppointments) . " appointments for clinic {$clinicId}");
                $this->logInfo("Found " . count($wpAppointments) . " appointments for clinic {$clinicId}");

                $processed = 0;
                $skipped = 0;
                $errors = 0;

                // 2. Process each appointment
                foreach ($wpAppointments as $wpAppointment) {
                    try {
                        // Find patient by email (your email-based approach)
                        $patientEmail = $wpAppointment['patient']['email'] ?? $wpAppointment['patient_email'] ?? null;
                        $providerEmail = $wpAppointment['doctor']['email'] ?? $wpAppointment['doctor_email'] ?? null;

                        if ($this->isDryRun()) {
                            $appointmentDate = $wpAppointment['appointment_start_date'] ?? 'Unknown';
                            $serviceName = $wpAppointment['services'] ?? 'No service';
                            $this->info("Would migrate appointment: {$patientEmail} → {$providerEmail} on {$appointmentDate} ({$serviceName})");
                            $this->logInfo("DRY RUN: Would migrate appointment ID {$wpAppointment['id']} for {$patientEmail}");
                            $processed++;
                            continue;
                        }

                        if (!$patientEmail) {
                            $this->error("Skipped appointment ID {$wpAppointment['id']}: No patient email");
                            $this->logError("Skipped appointment ID {$wpAppointment['id']}: No patient email", $wpAppointment);
                            $skipped++;
                            continue;
                        }

                        $patientUser = User::where('email', $patientEmail)->first();
                        if (!$patientUser) {
                            $this->error("Skipped appointment ID {$wpAppointment['id']}: Patient user not found ({$patientEmail})");
                            $this->logError("Skipped appointment ID {$wpAppointment['id']}: Patient user not found", ['email' => $patientEmail]);
                            $skipped++;
                            continue;
                        }

                        // Find or create patient record
                        $patient = $patientUser->patient;
                        if (!$patient) {
                            // Create patient record if it doesn't exist (without session)
                            try {
                                $patient = Patient::create([
                                    'user_id' => $patientUser->id,
                                    'clinic_id' => $patientUser->clinic_id,
                                ]);
                                $this->info("Created missing patient record for user {$patientEmail}");
                            } catch (\Exception $e) {
                                // If creation fails, try to find existing patient by user_id
                                $patient = Patient::where('user_id', $patientUser->id)->first();
                                if (!$patient) {
                                    $this->error("Failed to create/find patient record for {$patientEmail}: " . $e->getMessage());
                                    $skipped++;
                                    continue;
                                }
                            }
                        }

                        // Find provider by email
                        $providerUser = $providerEmail ? User::where('email', $providerEmail)->first() : null;
                        $provider = null;
                        if ($providerUser) {
                            $provider = $providerUser->provider;
                        }

                        // Transform WordPress appointment data to Laravel format
                        $laravelData = $this->transformer->transformAppointmentSimple($wpAppointment);
                        $laravelData['patient_id'] = $patient->id;
                        $laravelData['provider_id'] = $provider ? $provider->id : null;
                        $laravelData['wp_appointment_id'] = $wpAppointment['id'];

                        // Map clinic ID from WordPress clinic data
                        $wpClinicId = $wpAppointment['clinic']['id'] ?? null;
                        if ($wpClinicId) {
                            $laravelData['clinic_id'] = $this->transformer->findClinicByWpId($wpClinicId);
                        }

                        // Map service ID from service name
                        $serviceName = $wpAppointment['services'] ?? null;
                        if ($serviceName && $provider) {
                            $serviceId = $this->transformer->findServiceByName(
                                $serviceName,
                                $provider->id,
                                $laravelData['clinic_id'] ?? null
                            );
                            if ($serviceId) {
                                $laravelData['service_id'] = $serviceId;
                            }
                        }

                        // Create or update appointment
                        $appointment = Appointment::updateOrCreate(
                            ['wp_appointment_id' => $wpAppointment['id']],
                            $laravelData
                        );

                        $serviceInfo = $serviceName ? " (Service: {$serviceName})" : "";
                        $this->info("✓ Migrated appointment: ID {$appointment->id} ({$patientEmail} → " . ($providerEmail ?? 'No provider') . "){$serviceInfo}");
                        $this->logInfo("Successfully migrated appointment ID {$wpAppointment['id']} → Laravel ID {$appointment->id}");
                        $processed++;

                    } catch (Exception $e) {
                        $this->error("✗ Failed to migrate appointment ID {$wpAppointment['id']}: " . $e->getMessage());
                        $this->logError("Failed to migrate appointment ID {$wpAppointment['id']}: " . $e->getMessage(), $wpAppointment);
                        $errors++;
                    }
                }

                $this->info("Clinic {$clinicId} completed: {$processed} processed, {$skipped} skipped, {$errors} errors");
                $this->logInfo("Clinic {$clinicId} completed: {$processed} processed, {$skipped} skipped, {$errors} errors");

                $totalProcessed += $processed;
                $totalSkipped += $skipped;
                $totalErrors += $errors;

            } catch (Exception $e) {
                $this->error("❌ Failed to process clinic {$clinicId}: " . $e->getMessage());
                $this->logError("Failed to process clinic {$clinicId}: " . $e->getMessage());
                $failedClinics[] = $clinicId;
                continue;
            }
        }

        // 3. Generate summary
        $this->generateSummary($totalProcessed, $totalSkipped, $totalErrors, $failedClinics);

        return 0;
    }

    /**
     * Generate migration summary
     */
    protected function generateSummary($processed, $skipped, $errors, $failedClinics)
    {
        $this->info("\n" . str_repeat("=", 60));
        $this->info("📊 APPOINTMENTS MIGRATION SUMMARY");
        $this->info(str_repeat("=", 60));
        $this->info("✅ Processed: {$processed}");
        $this->info("⏭️  Skipped: {$skipped}");
        $this->info("❌ Errors: {$errors}");
        
        if (!empty($failedClinics)) {
            $this->error("🚨 Failed Clinics: " . implode(', ', $failedClinics));
        }
        
        if ($errors > 0 || !empty($failedClinics)) {
            $this->error("\n⚠️  Some appointments failed to migrate. Check error logs for details.");
        } else {
            $this->info("\n🎉 All appointments migrated successfully!");
        }
        
        $this->info("📁 Logs saved to: storage/logs/migration/appointments/");
        $this->info(str_repeat("=", 60));

        // Log summary
        $this->logInfo("Migration completed - Processed: {$processed}, Skipped: {$skipped}, Errors: {$errors}");
        if (!empty($failedClinics)) {
            $this->logError("Failed clinics: " . implode(', ', $failedClinics));
        }
    }

    /**
     * Log info message
     */
    protected function logInfo($message, $context = [])
    {
        Log::channel($this->logChannel)->info($message, $context);
    }

    /**
     * Log error message
     */
    protected function logError($message, $context = [])
    {
        Log::channel($this->errorLogChannel)->error($message, $context);
    }
}
