[2025-07-22 13:11:40] local.INFO: Starting migration command: migratewp:categories  
[2025-07-22 13:11:40] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:11:49] local.INFO: WordPress API connection validated  
[2025-07-22 13:11:50] local.INFO: Database connection validated  
[2025-07-22 13:11:50] local.INFO: Starting categories migration  
[2025-07-22 13:11:50] local.INFO: Running in DRY RUN mode  
[2025-07-22 13:11:50] local.INFO: Making KiviCare API request: laravel_get_static_data  
[2025-07-22 13:11:55] local.INFO: No categories found in static data  
[2025-07-22 13:11:55] local.INFO: Migration command completed successfully  
[2025-07-22 13:12:18] local.INFO: Starting migration command: migratewp:categories  
[2025-07-22 13:12:18] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:12:22] local.INFO: WordPress API connection validated  
[2025-07-22 13:12:22] local.INFO: Database connection validated  
[2025-07-22 13:12:22] local.INFO: Starting categories migration  
[2025-07-22 13:12:22] local.INFO: Running in DRY RUN mode  
[2025-07-22 13:12:22] local.INFO: Making KiviCare API request: laravel_get_static_data  
[2025-07-22 13:12:27] local.INFO: No categories found in static data  
[2025-07-22 13:12:27] local.INFO: Migration command completed successfully  
[2025-07-22 13:14:56] local.INFO: Starting migration command: migratewp:categories  
[2025-07-22 13:14:56] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:15:03] local.INFO: WordPress API connection validated  
[2025-07-22 13:15:03] local.INFO: Database connection validated  
[2025-07-22 13:15:03] local.INFO: Starting categories migration  
[2025-07-22 13:15:03] local.INFO: Running in DRY RUN mode  
[2025-07-22 13:15:03] local.INFO: Getting categories for clinic 14  
[2025-07-22 13:15:03] local.INFO: Making KiviCare API request: laravel_get_clinic_static_data  
[2025-07-22 13:15:07] local.INFO: No categories found for clinic 14  
[2025-07-22 13:15:07] local.INFO: Migration completed - Processed: 0, Skipped: 0, Errors: 0  
[2025-07-22 13:15:07] local.INFO: Migration command completed successfully  
[2025-07-22 13:18:48] local.INFO: Starting migration command: migratewp:categories  
[2025-07-22 13:18:48] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:18:53] local.INFO: WordPress API connection validated  
[2025-07-22 13:18:53] local.INFO: Database connection validated  
[2025-07-22 13:18:53] local.INFO: Starting categories migration  
[2025-07-22 13:18:53] local.INFO: Running in DRY RUN mode  
[2025-07-22 13:18:53] local.INFO: Getting global categories  
[2025-07-22 13:18:53] local.INFO: Making KiviCare API request: laravel_get_static_data  
[2025-07-22 13:18:59] local.INFO: No categories found in global static data  
[2025-07-22 13:18:59] local.INFO: Migration command completed successfully  
[2025-07-22 13:19:29] local.INFO: Starting migration command: migratewp:categories  
[2025-07-22 13:19:29] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:19:38] local.INFO: WordPress API connection validated  
[2025-07-22 13:19:38] local.INFO: Database connection validated  
[2025-07-22 13:19:38] local.INFO: Starting categories migration  
[2025-07-22 13:19:38] local.INFO: Running in DRY RUN mode  
[2025-07-22 13:19:38] local.INFO: Getting global categories  
[2025-07-22 13:19:38] local.INFO: Making KiviCare API request: get_public_categories  
[2025-07-22 13:19:43] local.INFO: No categories found in global static data  
[2025-07-22 13:19:43] local.INFO: Migration command completed successfully  
[2025-07-22 13:25:53] local.INFO: Starting migration command: migratewp:categories  
[2025-07-22 13:25:53] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:26:00] local.INFO: WordPress API connection validated  
[2025-07-22 13:26:00] local.INFO: Database connection validated  
[2025-07-22 13:26:00] local.INFO: Starting categories migration  
[2025-07-22 13:26:00] local.INFO: Running in DRY RUN mode  
[2025-07-22 13:26:00] local.INFO: Getting global categories  
[2025-07-22 13:26:00] local.INFO: Making KiviCare API request: laravel_get_static_data  
[2025-07-22 13:26:05] local.INFO: Found 49 global categories  
[2025-07-22 13:26:05] local.INFO: Migration command completed successfully  
[2025-07-22 13:26:55] local.INFO: Starting migration command: migratewp:categories  
[2025-07-22 13:26:55] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:27:01] local.INFO: WordPress API connection validated  
[2025-07-22 13:27:02] local.INFO: Database connection validated  
[2025-07-22 13:27:02] local.INFO: Starting categories migration  
[2025-07-22 13:27:02] local.INFO: Running in DRY RUN mode  
[2025-07-22 13:27:02] local.INFO: Getting global categories  
[2025-07-22 13:27:02] local.INFO: Making KiviCare API request: laravel_get_static_data  
[2025-07-22 13:27:07] local.INFO: Found 49 global categories  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 39: Acupuncture  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 15: ADMIN  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 17: Aesthetics  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 47: Bill Service  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 5: Blood Tests  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 43: Bloods  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 16: Coaching  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 9: Consultation  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 44: Diagnostics  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 38: ECG  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 1: General Dentistry  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 4: GP  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 20: Health Checks  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 23: Heart Health  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 12: Intramuscular Injections  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 31: IV Nutrition  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 29: Joint Injections  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 45: Medication  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 19: Men's Health  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 10: Microsuction  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 8: Minor Illness  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 7: Minor ilness  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 6: Mole Check  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 13: Phelbotomy  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 37: Phlebotomy  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 3: Psychology Services  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 35: Rivaxis  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 41: Scan  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 42: Scans  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 36: sexual health  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 21: Skin Clinic  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 27: Specialists  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 33: STD Swab  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 40: Stool  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 34: Suture  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 50: test1  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 28: Throat Swab  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 22: Ultrasound  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 14: Urgent Care  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 25: Urine Culture  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 30: Urine Dip Stick  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 26: Urine STD Culture  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 49: Urine Tests  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 32: Urology  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 2: Weight Management  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 18: Women's Health  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 11: Wound Care  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 48: Wound Health  
[2025-07-22 13:27:07] local.INFO: DRY RUN: Would migrate category ID 24: Wound Swab  
[2025-07-22 13:27:07] local.INFO: Migration completed - Processed: 49, Skipped: 0, Errors: 0  
[2025-07-22 13:27:07] local.INFO: Migration command completed successfully  
[2025-07-22 13:27:19] local.INFO: Starting migration command: migratewp:categories  
[2025-07-22 13:27:19] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 13:27:24] local.INFO: WordPress API connection validated  
[2025-07-22 13:27:24] local.INFO: Database connection validated  
[2025-07-22 13:27:24] local.INFO: Starting categories migration  
[2025-07-22 13:27:24] local.INFO: Getting global categories  
[2025-07-22 13:27:24] local.INFO: Making KiviCare API request: laravel_get_static_data  
[2025-07-22 13:27:29] local.INFO: Found 49 global categories  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 39 → Laravel ID 1: Acupuncture  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 15 → Laravel ID 2: ADMIN  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 17 → Laravel ID 3: Aesthetics  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 47 → Laravel ID 4: Bill Service  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 5 → Laravel ID 5: Blood Tests  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 43 → Laravel ID 6: Bloods  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 16 → Laravel ID 7: Coaching  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 9 → Laravel ID 8: Consultation  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 44 → Laravel ID 9: Diagnostics  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 38 → Laravel ID 10: ECG  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 1 → Laravel ID 11: General Dentistry  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 4 → Laravel ID 12: GP  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 20 → Laravel ID 13: Health Checks  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 23 → Laravel ID 14: Heart Health  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 12 → Laravel ID 15: Intramuscular Injections  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 31 → Laravel ID 16: IV Nutrition  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 29 → Laravel ID 17: Joint Injections  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 45 → Laravel ID 18: Medication  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 19 → Laravel ID 19: Men's Health  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 10 → Laravel ID 20: Microsuction  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 8 → Laravel ID 21: Minor Illness  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 7 → Laravel ID 22: Minor ilness  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 6 → Laravel ID 23: Mole Check  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 13 → Laravel ID 24: Phelbotomy  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 37 → Laravel ID 25: Phlebotomy  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 3 → Laravel ID 26: Psychology Services  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 35 → Laravel ID 27: Rivaxis  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 41 → Laravel ID 28: Scan  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 42 → Laravel ID 29: Scans  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 36 → Laravel ID 30: sexual health  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 21 → Laravel ID 31: Skin Clinic  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 27 → Laravel ID 32: Specialists  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 33 → Laravel ID 33: STD Swab  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 40 → Laravel ID 34: Stool  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 34 → Laravel ID 35: Suture  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 50 → Laravel ID 36: test1  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 28 → Laravel ID 37: Throat Swab  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 22 → Laravel ID 38: Ultrasound  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 14 → Laravel ID 39: Urgent Care  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 25 → Laravel ID 40: Urine Culture  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 30 → Laravel ID 41: Urine Dip Stick  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 26 → Laravel ID 42: Urine STD Culture  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 49 → Laravel ID 43: Urine Tests  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 32 → Laravel ID 44: Urology  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 2 → Laravel ID 45: Weight Management  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 18 → Laravel ID 46: Women's Health  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 11 → Laravel ID 47: Wound Care  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 48 → Laravel ID 48: Wound Health  
[2025-07-22 13:27:29] local.INFO: Successfully migrated category ID 24 → Laravel ID 49: Wound Swab  
[2025-07-22 13:27:29] local.INFO: Migration completed - Processed: 49, Skipped: 0, Errors: 0  
[2025-07-22 13:27:29] local.INFO: Migration command completed successfully  
