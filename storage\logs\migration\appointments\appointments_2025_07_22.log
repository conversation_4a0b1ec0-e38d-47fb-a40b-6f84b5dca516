[2025-07-22 15:55:35] local.INFO: Starting migration command: migratewp:appointments  
[2025-07-22 15:55:35] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 15:55:42] local.INFO: WordPress API connection validated  
[2025-07-22 15:55:42] local.INFO: Database connection validated  
[2025-07-22 15:55:42] local.INFO: Starting appointments migration  
[2025-07-22 15:55:42] local.INFO: Running in DRY RUN mode  
[2025-07-22 15:55:42] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 15:55:46] local.INFO: Starting appointments migration for clinic 40  
[2025-07-22 15:55:46] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:55:51] local.INFO: Found 5 appointments for clinic 40  
[2025-07-22 15:55:51] local.INFO: DRY RUN: Would migrate appointment ID 1050 for   
[2025-07-22 15:55:51] local.INFO: DRY RUN: Would migrate appointment ID 1002 for   
[2025-07-22 15:55:51] local.INFO: DRY RUN: Would migrate appointment ID 958 for   
[2025-07-22 15:55:51] local.INFO: DRY RUN: Would migrate appointment ID 960 for   
[2025-07-22 15:55:51] local.INFO: DRY RUN: Would migrate appointment ID 959 for   
[2025-07-22 15:55:51] local.INFO: Clinic 40 completed: 5 processed, 0 skipped, 0 errors  
[2025-07-22 15:55:51] local.INFO: Starting appointments migration for clinic 27  
[2025-07-22 15:55:51] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:55:56] local.INFO: No appointments found for clinic 27  
[2025-07-22 15:55:56] local.INFO: Starting appointments migration for clinic 26  
[2025-07-22 15:55:56] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:02] local.INFO: No appointments found for clinic 26  
[2025-07-22 15:56:02] local.INFO: Starting appointments migration for clinic 22  
[2025-07-22 15:56:02] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:06] local.INFO: No appointments found for clinic 22  
[2025-07-22 15:56:06] local.INFO: Starting appointments migration for clinic 16  
[2025-07-22 15:56:06] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:09] local.INFO: Found 17 appointments for clinic 16  
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 1431 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 1432 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 1416 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 911 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 863 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 851 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 807 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 803 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 680 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 631 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 617 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 559 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 550 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 553 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 543 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 402 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 398 for   
[2025-07-22 15:56:09] local.INFO: Clinic 16 completed: 17 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:09] local.INFO: Starting appointments migration for clinic 15  
[2025-07-22 15:56:09] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:13] local.INFO: No appointments found for clinic 15  
[2025-07-22 15:56:13] local.INFO: Starting appointments migration for clinic 14  
[2025-07-22 15:56:13] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:18] local.INFO: Found 580 appointments for clinic 14  
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1395 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1379 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1331 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1319 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1413 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1390 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1427 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1398 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1418 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1415 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1414 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1412 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1409 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1298 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1405 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1404 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1410 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1408 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1402 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1403 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1396 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1389 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1400 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1399 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1394 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1385 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1384 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1383 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1381 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1380 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1378 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1353 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1377 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1237 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1375 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1369 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1356 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1368 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1358 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1300 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1301 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1360 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1362 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1359 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1332 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1350 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1349 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1347 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1322 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1338 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1303 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1312 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1340 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1335 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1336 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1103 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1337 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1334 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1124 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1275 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1325 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1326 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1304 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1330 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1328 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1323 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1321 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1320 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1308 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1306 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1307 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1302 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1297 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1250 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1295 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1293 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1292 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1291 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1288 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1245 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1280 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1282 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1276 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1277 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1205 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1274 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1273 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1270 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1215 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1244 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1271 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1267 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1258 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1264 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1249 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1259 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1263 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1257 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1200 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1246 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1216 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1243 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1241 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1222 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1221 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1220 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1192 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1218 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1212 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1214 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1213 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1159 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1201 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1184 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1197 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1208 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1209 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1206 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1196 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1190 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1180 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1123 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1179 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1193 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1183 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1191 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1189 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1188 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1186 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1166 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1160 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1177 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1174 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1176 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1171 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1170 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1165 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1168 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1169 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1167 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1162 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1161 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1158 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1152 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1157 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1146 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1154 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1151 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1147 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1142 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1145 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1144 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1143 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1141 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1139 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1136 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1140 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1133 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1132 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1131 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1129 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1128 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1125 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1121 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1120 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1117 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1119 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1118 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1114 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1116 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1115 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1112 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1113 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1111 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1100 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1110 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1099 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 944 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1107 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1102 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1097 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1054 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1087 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1096 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1045 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1090 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1093 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1094 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1091 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1089 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1075 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1086 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1085 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1083 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1084 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1082 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1081 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1079 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1080 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1078 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1076 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1074 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1069 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1064 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1052 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1037 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1019 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1009 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1024 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1062 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1057 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1060 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1059 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1055 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1053 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1056 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1017 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1051 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1029 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1040 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1038 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 986 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1035 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1034 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1031 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1028 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1026 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1027 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1018 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1021 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1013 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1016 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1008 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1014 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1005 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1012 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1006 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1004 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 999 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 995 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 996 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 994 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 993 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 992 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 955 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 987 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 981 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 984 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 985 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 961 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 978 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 980 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 976 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 968 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 970 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 969 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 966 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 941 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 948 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 965 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 935 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 887 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 956 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 848 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 940 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 953 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 950 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 881 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 902 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 942 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 938 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 936 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 937 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 915 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 933 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 934 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 932 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 874 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 891 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 931 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 928 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 913 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 929 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 927 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 926 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 920 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 924 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 914 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 921 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 919 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 908 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 912 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 905 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 897 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 896 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 904 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 853 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 899 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 850 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 893 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 892 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 864 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 890 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 889 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 888 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 886 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 885 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 884 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 882 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 877 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 883 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 829 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 880 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 879 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 830 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 858 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 875 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 876 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 838 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 873 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 872 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 871 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 870 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 869 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 868 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 862 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 861 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 860 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 859 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 856 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 854 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 852 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 844 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 795 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 847 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 841 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 843 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 839 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 837 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 836 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 835 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 834 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 833 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 775 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 831 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 832 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 828 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 784 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 827 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 826 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 825 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 824 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 813 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 819 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 816 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 810 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 814 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 812 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 811 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 806 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 805 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 802 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 727 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 799 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 797 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 793 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 714 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 791 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 792 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 789 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 777 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 787 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 743 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 783 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 781 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 779 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 778 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 741 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 772 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 774 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 770 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 702 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 771 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 769 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 768 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 742 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 740 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 732 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 739 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 726 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 729 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 719 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 639 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 551 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 683 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 677 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 707 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 712 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 709 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 711 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 693 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 710 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 708 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 706 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 705 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 704 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 694 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 696 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 692 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 697 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 666 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 691 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 688 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 689 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 686 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 685 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 653 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 679 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 665 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 667 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 668 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 661 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 657 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 656 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 654 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 652 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 651 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 650 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 633 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 644 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 649 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 646 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 648 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 642 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 618 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 641 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 632 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 640 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 540 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 568 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 638 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 525 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 637 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 634 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 635 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 628 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 629 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 626 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 625 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 557 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 507 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 567 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 565 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 546 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 524 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 552 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 542 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 539 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 535 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 527 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 538 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 536 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 510 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 526 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 494 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 502 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 522 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 470 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 523 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 520 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 487 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 484 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 516 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 515 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 514 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 511 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 436 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 506 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 500 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 501 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 497 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 493 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 491 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 483 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 477 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 475 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 474 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 446 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 473 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 469 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 471 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 468 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 466 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 462 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 463 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 459 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 460 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 457 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 456 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 453 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 449 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 444 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 378 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 435 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 278 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 433 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 430 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 428 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 426 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 413 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 424 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 411 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 393 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 408 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 407 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 406 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 405 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 404 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 328 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 396 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 391 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 392 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 351 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 388 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 377 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 376 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 365 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 371 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 370 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 367 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 364 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 362 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 360 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 356 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 357 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 353 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 352 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 337 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 350 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 331 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 333 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 347 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 348 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 345 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 346 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 335 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 332 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 323 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 330 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 327 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 322 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 308 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 300 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 299 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 284 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 285 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 283 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 270 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 281 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 280 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 263 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 279 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 271 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 258 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 273 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 272 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 261 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 264 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 262 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 247 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 244 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 241 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 226 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 221 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 236 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 219 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 231 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 220 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 224 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 217 for   
[2025-07-22 15:56:19] local.INFO: Clinic 14 completed: 580 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:19] local.INFO: Starting appointments migration for clinic 13  
[2025-07-22 15:56:19] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:22] local.INFO: Found 1 appointments for clinic 13  
[2025-07-22 15:56:22] local.INFO: DRY RUN: Would migrate appointment ID 81 for   
[2025-07-22 15:56:22] local.INFO: Clinic 13 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:22] local.INFO: Starting appointments migration for clinic 12  
[2025-07-22 15:56:22] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:25] local.INFO: Found 22 appointments for clinic 12  
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1374 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1371 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1370 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1345 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1313 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1279 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1242 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1239 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1149 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1036 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1023 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 997 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 952 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 962 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 951 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 643 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 623 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 548 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 519 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 455 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 401 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 338 for   
[2025-07-22 15:56:25] local.INFO: Clinic 12 completed: 22 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:25] local.INFO: Starting appointments migration for clinic 11  
[2025-07-22 15:56:25] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:29] local.INFO: Found 305 appointments for clinic 11  
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1406 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1397 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1388 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1382 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1365 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1372 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1367 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1364 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1363 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1361 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1354 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1352 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1348 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1324 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1344 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1343 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1329 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1333 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1314 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1309 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1299 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1289 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1290 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1284 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1285 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1281 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1287 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1286 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1278 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1272 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1268 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1265 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1266 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1256 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1253 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1254 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1248 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1207 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1219 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1217 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1240 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1235 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1234 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1224 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1233 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1211 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1198 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1199 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1194 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1185 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1163 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1155 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1156 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1181 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1182 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1172 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1134 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1138 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1135 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1127 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1126 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1108 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1105 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1101 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1104 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1095 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1092 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1088 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1070 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1071 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1068 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1061 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1048 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1049 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1033 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1030 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1025 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1020 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1010 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1007 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1003 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 998 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 991 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 990 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 989 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 988 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 971 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 967 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 954 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 918 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 946 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 945 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 939 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 930 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 925 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 922 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 923 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 878 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 909 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 910 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 916 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 907 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 867 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 840 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 738 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 809 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 808 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 801 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 798 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 796 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 794 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 788 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 737 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 776 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 725 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 767 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 718 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 730 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 721 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 717 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 564 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 715 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 695 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 687 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 684 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 678 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 681 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 682 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 675 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 669 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 664 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 660 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 663 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 636 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 569 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 566 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 563 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 556 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 531 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 532 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 541 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 530 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 495 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 496 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 505 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 529 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 513 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 512 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 504 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 489 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 488 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 479 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 482 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 480 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 481 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 465 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 454 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 432 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 472 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 464 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 461 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 458 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 450 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 442 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 443 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 434 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 439 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 437 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 431 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 429 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 427 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 409 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 421 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 399 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 394 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 395 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 390 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 389 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 379 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 369 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 374 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 372 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 341 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 349 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 344 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 334 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 343 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 340 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 317 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 324 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 316 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 314 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 306 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 310 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 304 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 298 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 295 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 282 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 294 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 277 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 268 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 269 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 266 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 260 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 259 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 257 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 255 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 249 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 248 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 242 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 240 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 235 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 234 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 233 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 232 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 228 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 225 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 227 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 222 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 223 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 215 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 214 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 209 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 182 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 176 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 194 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 180 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 178 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 179 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 173 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 171 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 165 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 164 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 163 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 158 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 150 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 154 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 153 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 147 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 144 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 132 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 141 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 142 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 143 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 120 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 121 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 134 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 131 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 139 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 130 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 129 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 127 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 125 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 126 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 118 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 124 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 115 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 123 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 122 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 116 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 119 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 112 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 113 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 114 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 111 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 110 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 109 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 108 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 105 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 107 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 104 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 103 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 99 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 97 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 98 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 96 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 95 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 93 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 92 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 89 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 90 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 91 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 88 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 87 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 86 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 83 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 84 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 85 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 77 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 76 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 73 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 72 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 71 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 70 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 67 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 66 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 64 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 63 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 17 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 23 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 24 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 19 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 15 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 14 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 13 for   
[2025-07-22 15:56:30] local.INFO: Clinic 11 completed: 305 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:30] local.INFO: Starting appointments migration for clinic 10  
[2025-07-22 15:56:30] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:33] local.INFO: Found 1 appointments for clinic 10  
[2025-07-22 15:56:33] local.INFO: DRY RUN: Would migrate appointment ID 136 for   
[2025-07-22 15:56:33] local.INFO: Clinic 10 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:33] local.INFO: Starting appointments migration for clinic 9  
[2025-07-22 15:56:33] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:37] local.INFO: Found 2 appointments for clinic 9  
[2025-07-22 15:56:37] local.INFO: DRY RUN: Would migrate appointment ID 62 for   
[2025-07-22 15:56:37] local.INFO: DRY RUN: Would migrate appointment ID 61 for   
[2025-07-22 15:56:37] local.INFO: Clinic 9 completed: 2 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:37] local.INFO: Starting appointments migration for clinic 8  
[2025-07-22 15:56:37] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:41] local.INFO: Found 7 appointments for clinic 8  
[2025-07-22 15:56:41] local.INFO: DRY RUN: Would migrate appointment ID 658 for   
[2025-07-22 15:56:41] local.INFO: DRY RUN: Would migrate appointment ID 616 for   
[2025-07-22 15:56:41] local.INFO: DRY RUN: Would migrate appointment ID 410 for   
[2025-07-22 15:56:41] local.INFO: DRY RUN: Would migrate appointment ID 326 for   
[2025-07-22 15:56:41] local.INFO: DRY RUN: Would migrate appointment ID 265 for   
[2025-07-22 15:56:41] local.INFO: DRY RUN: Would migrate appointment ID 146 for   
[2025-07-22 15:56:41] local.INFO: DRY RUN: Would migrate appointment ID 100 for   
[2025-07-22 15:56:41] local.INFO: Clinic 8 completed: 7 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:41] local.INFO: Starting appointments migration for clinic 6  
[2025-07-22 15:56:41] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:45] local.INFO: No appointments found for clinic 6  
[2025-07-22 15:56:45] local.INFO: Starting appointments migration for clinic 5  
[2025-07-22 15:56:45] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:50] local.INFO: Found 38 appointments for clinic 5  
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1148 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1341 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1260 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1261 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1044 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1043 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1072 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1073 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1047 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1041 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1042 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 731 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 508 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 857 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 701 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 700 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 723 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 722 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 724 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 699 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 698 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 561 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 624 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 674 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 254 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 375 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 301 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 207 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 303 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 102 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 162 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 106 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 47 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 94 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 46 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 45 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 74 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 25 for   
[2025-07-22 15:56:50] local.INFO: Clinic 5 completed: 38 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:50] local.INFO: Starting appointments migration for clinic 4  
[2025-07-22 15:56:50] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:53] local.INFO: No appointments found for clinic 4  
[2025-07-22 15:56:53] local.INFO: Starting appointments migration for clinic 2  
[2025-07-22 15:56:53] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:58] local.INFO: No appointments found for clinic 2  
[2025-07-22 15:56:58] local.INFO: Starting appointments migration for clinic 1  
[2025-07-22 15:56:58] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:57:00] local.INFO: Found 24 appointments for clinic 1  
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 313 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 161 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 137 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 160 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 159 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 151 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 155 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 156 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 157 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 133 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 135 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 54 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 56 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 80 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 79 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 82 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 65 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 43 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 20 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 55 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 60 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 58 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 57 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 18 for   
[2025-07-22 15:57:00] local.INFO: Clinic 1 completed: 24 processed, 0 skipped, 0 errors  
[2025-07-22 15:57:00] local.INFO: Migration completed - Processed: 1002, Skipped: 0, Errors: 0  
[2025-07-22 15:57:00] local.INFO: Migration command completed successfully  
[2025-07-22 16:28:18] local.INFO: Starting migration command: migratewp:appointments  
[2025-07-22 16:28:18] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 16:28:23] local.INFO: WordPress API connection validated  
[2025-07-22 16:28:23] local.INFO: Database connection validated  
[2025-07-22 16:28:23] local.INFO: Starting appointments migration  
[2025-07-22 16:28:23] local.INFO: Running in DRY RUN mode  
[2025-07-22 16:28:23] local.INFO: Starting appointments migration for clinic 14  
[2025-07-22 16:28:23] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 16:28:29] local.INFO: Found 580 appointments for clinic 14  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1395 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1379 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1331 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1319 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1413 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1390 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1427 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1398 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1418 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1415 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1414 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1412 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1409 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1298 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1405 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1404 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1410 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1408 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1402 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1403 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1396 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1389 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1400 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1399 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1394 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1385 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1384 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1383 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1381 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1380 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1378 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1353 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1377 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1237 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1375 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1369 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1356 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1368 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1358 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1300 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1301 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1360 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1362 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1359 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1332 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1350 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1349 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1347 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1322 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1338 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1303 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1312 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1340 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1335 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1336 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1103 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1337 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1334 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1124 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1275 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1325 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1326 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1304 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1330 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1328 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1323 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1321 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1320 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1308 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1306 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1307 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1302 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1297 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1250 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1295 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1293 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1292 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1291 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1288 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1245 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1280 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1282 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1276 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1277 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1205 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1274 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1273 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1270 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1215 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1244 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1271 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1267 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1258 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1264 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1249 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1259 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1263 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1257 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1200 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1246 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1216 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1243 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1241 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1222 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1221 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1220 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1192 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1218 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1212 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1214 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1213 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1159 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1201 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1184 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1197 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1208 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1209 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1206 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1196 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1190 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1180 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1123 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1179 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1193 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1183 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1191 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1189 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1188 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1186 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1166 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1160 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1177 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1174 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1176 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1171 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1170 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1165 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1168 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1169 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1167 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1162 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1161 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1158 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1152 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1157 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1146 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1154 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1151 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1147 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1142 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1145 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1144 for peterclift123**@gmail.com  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1143 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1141 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1139 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1136 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1140 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1133 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1132 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1131 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1129 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1128 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1125 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1121 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1120 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1117 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1119 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1118 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1114 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1116 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1115 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1112 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1113 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1111 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1100 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1110 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1099 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 944 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1107 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1102 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1097 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1054 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1087 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1096 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1045 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1090 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1093 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1094 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1091 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1089 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1075 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1086 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1085 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1083 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1084 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1082 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1081 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1079 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1080 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1078 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1076 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1074 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1069 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1064 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1052 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1037 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1019 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1009 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1024 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1062 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1057 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1060 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1059 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1055 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1053 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1056 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1017 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1051 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1029 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1040 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1038 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 986 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1035 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1034 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1031 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1028 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1026 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1027 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1018 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1021 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1013 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1016 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1008 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1014 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1005 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1012 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1006 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 1004 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 999 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 995 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 996 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 994 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 993 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 992 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 955 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 987 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 981 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 984 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 985 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 961 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 978 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 980 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 976 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 968 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 970 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 969 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 966 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 941 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 948 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 965 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 935 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 887 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 956 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 848 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 940 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 953 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 950 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 881 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 902 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 942 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 938 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 936 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 937 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 915 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 933 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 934 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 932 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 874 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 891 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 931 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 928 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 913 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 929 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 927 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 926 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 920 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 924 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 914 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 921 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 919 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 908 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 912 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 905 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 897 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 896 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 904 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 853 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 899 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 850 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 893 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 892 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 864 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 890 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 889 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 888 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 886 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 885 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 884 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 882 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 877 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 883 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 829 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 880 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 879 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 830 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 858 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 875 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 876 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 838 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 873 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 872 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 871 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 870 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 869 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 868 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 862 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 861 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 860 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 859 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 856 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 854 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 852 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 844 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 795 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 847 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 841 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 843 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 839 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 837 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 836 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 835 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 834 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 833 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 775 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 831 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 832 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 828 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 784 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 827 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 826 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 825 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 824 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 813 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 819 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 816 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 810 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 814 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 812 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 811 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 806 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 805 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 802 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 727 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 799 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 797 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 793 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 714 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 791 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 792 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 789 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 777 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 787 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 743 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 783 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 781 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 779 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 778 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 741 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 772 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 774 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 770 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 702 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 771 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 769 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 768 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 742 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 740 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 732 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 739 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 726 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 729 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 719 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 639 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 551 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 683 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 677 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 707 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 712 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 709 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 711 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 693 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 710 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 708 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 706 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 705 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 704 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 694 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 696 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 692 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 697 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 666 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 691 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 688 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 689 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 686 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 685 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 653 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 679 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 665 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 667 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 668 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 661 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 657 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 656 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 654 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 652 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 651 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 650 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 633 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 644 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 649 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 646 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 648 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 642 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 618 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 641 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 632 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 640 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 540 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 568 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 638 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 525 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 637 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 634 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 635 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 628 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 629 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 626 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 625 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 557 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 507 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 567 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 565 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 546 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 524 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 552 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 542 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 539 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 535 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 527 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 538 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 536 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 510 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 526 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 494 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 502 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 522 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 470 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 523 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 520 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 487 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 484 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 516 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 515 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 514 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 511 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 436 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 506 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 500 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 501 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 497 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 493 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 491 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 483 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 477 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 475 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 474 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 446 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 473 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 469 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 471 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 468 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 466 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 462 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 463 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 459 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 460 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 457 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 456 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 453 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 449 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 444 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 378 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 435 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 278 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 433 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 430 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 428 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 426 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 413 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 424 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 411 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 393 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 408 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 407 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 406 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 405 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 404 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 328 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 396 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 391 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 392 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 351 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 388 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 377 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 376 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 365 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 371 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 370 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 367 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 364 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 362 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 360 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 356 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 357 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 353 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 352 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 337 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 350 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 331 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 333 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 347 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 348 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 345 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 346 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 335 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 332 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 323 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 330 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 327 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 322 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 308 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 300 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 299 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 284 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 285 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 283 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 270 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 281 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 280 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 263 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 279 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 271 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 258 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 273 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 272 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 261 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 264 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 262 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 247 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 244 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 241 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 226 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 221 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 236 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 219 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 231 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 220 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 224 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: DRY RUN: Would migrate appointment ID 217 for <EMAIL>  
[2025-07-22 16:28:29] local.INFO: Clinic 14 completed: 580 processed, 0 skipped, 0 errors  
[2025-07-22 16:28:29] local.INFO: Migration completed - Processed: 580, Skipped: 0, Errors: 0  
[2025-07-22 16:28:29] local.INFO: Migration command completed successfully  
[2025-07-22 16:28:38] local.INFO: Starting migration command: migratewp:appointments  
[2025-07-22 16:28:38] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 16:28:43] local.INFO: WordPress API connection validated  
[2025-07-22 16:28:43] local.INFO: Database connection validated  
[2025-07-22 16:28:43] local.INFO: Starting appointments migration  
[2025-07-22 16:28:43] local.INFO: Starting appointments migration for clinic 14  
[2025-07-22 16:28:43] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 16:28:46] local.INFO: Found 580 appointments for clinic 14  
[2025-07-22 16:28:54] local.INFO: Clinic 14 completed: 0 processed, 0 skipped, 580 errors  
[2025-07-22 16:28:54] local.INFO: Migration completed - Processed: 0, Skipped: 0, Errors: 580  
[2025-07-22 16:28:54] local.INFO: Migration command completed successfully  
[2025-07-22 16:32:06] local.INFO: Starting migration command: migratewp:appointments  
[2025-07-22 16:32:06] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 16:32:14] local.INFO: WordPress API connection validated  
[2025-07-22 16:32:14] local.INFO: Database connection validated  
[2025-07-22 16:32:14] local.INFO: Starting appointments migration  
[2025-07-22 16:32:14] local.INFO: Running in DRY RUN mode  
[2025-07-22 16:32:14] local.INFO: Starting appointments migration for clinic 14  
[2025-07-22 16:32:14] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 16:32:20] local.INFO: Found 580 appointments for clinic 14  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1395 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1379 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1331 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1319 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1413 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1390 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1427 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1398 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1418 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1415 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1414 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1412 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1409 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1298 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1405 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1404 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1410 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1408 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1402 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1403 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1396 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1389 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1400 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1399 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1394 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1385 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1384 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1383 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1381 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1380 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1378 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1353 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1377 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1237 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1375 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1369 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1356 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1368 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1358 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1300 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1301 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1360 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1362 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1359 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1332 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1350 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1349 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1347 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1322 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1338 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1303 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1312 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1340 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1335 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1336 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1103 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1337 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1334 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1124 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1275 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1325 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1326 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1304 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1330 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1328 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1323 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1321 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1320 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1308 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1306 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1307 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1302 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1297 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1250 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1295 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1293 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1292 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1291 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1288 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1245 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1280 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1282 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1276 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1277 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1205 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1274 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1273 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1270 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1215 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1244 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1271 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1267 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1258 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1264 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1249 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1259 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1263 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1257 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1200 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1246 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1216 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1243 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1241 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1222 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1221 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1220 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1192 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1218 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1212 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1214 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1213 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1159 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1201 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1184 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1197 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1208 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1209 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1206 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1196 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1190 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1180 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1123 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1179 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1193 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1183 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1191 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1189 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1188 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1186 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1166 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1160 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1177 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1174 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1176 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1171 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1170 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1165 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1168 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1169 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1167 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1162 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1161 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1158 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1152 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1157 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1146 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1154 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1151 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1147 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1142 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1145 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1144 for peterclift123**@gmail.com  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1143 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1141 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1139 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1136 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1140 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1133 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1132 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1131 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1129 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1128 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1125 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1121 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1120 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1117 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1119 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1118 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1114 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1116 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1115 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1112 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1113 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1111 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1100 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1110 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1099 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 944 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1107 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1102 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1097 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1054 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1087 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1096 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1045 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1090 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1093 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1094 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1091 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1089 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1075 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1086 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1085 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1083 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1084 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1082 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1081 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1079 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1080 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1078 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1076 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1074 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1069 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1064 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1052 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1037 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1019 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1009 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1024 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1062 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1057 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1060 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1059 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1055 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1053 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1056 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1017 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1051 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1029 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1040 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1038 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 986 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1035 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1034 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1031 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1028 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1026 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1027 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1018 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1021 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1013 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1016 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1008 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1014 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1005 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1012 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1006 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 1004 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 999 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 995 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 996 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 994 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 993 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 992 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 955 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 987 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 981 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 984 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 985 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 961 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 978 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 980 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 976 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 968 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 970 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 969 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 966 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 941 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 948 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 965 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 935 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 887 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 956 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 848 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 940 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 953 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 950 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 881 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 902 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 942 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 938 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 936 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 937 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 915 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 933 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 934 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 932 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 874 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 891 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 931 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 928 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 913 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 929 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 927 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 926 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 920 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 924 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 914 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 921 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 919 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 908 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 912 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 905 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 897 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 896 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 904 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 853 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 899 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 850 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 893 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 892 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 864 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 890 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 889 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 888 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 886 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 885 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 884 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 882 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 877 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 883 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 829 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 880 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 879 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 830 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 858 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 875 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 876 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 838 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 873 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 872 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 871 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 870 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 869 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 868 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 862 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 861 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 860 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 859 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 856 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 854 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 852 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 844 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 795 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 847 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 841 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 843 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 839 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 837 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 836 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 835 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 834 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 833 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 775 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 831 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 832 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 828 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 784 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 827 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 826 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 825 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 824 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 813 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 819 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 816 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 810 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 814 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 812 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 811 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 806 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 805 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 802 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 727 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 799 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 797 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 793 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 714 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 791 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 792 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 789 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 777 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 787 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 743 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 783 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 781 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 779 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 778 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 741 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 772 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 774 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 770 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 702 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 771 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 769 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 768 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 742 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 740 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 732 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 739 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 726 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 729 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 719 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 639 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 551 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 683 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 677 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 707 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 712 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 709 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 711 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 693 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 710 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 708 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 706 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 705 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 704 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 694 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 696 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 692 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 697 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 666 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 691 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 688 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 689 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 686 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 685 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 653 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 679 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 665 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 667 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 668 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 661 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 657 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 656 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 654 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 652 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 651 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 650 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 633 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 644 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 649 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 646 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 648 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 642 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 618 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 641 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 632 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 640 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 540 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 568 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 638 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 525 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 637 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 634 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 635 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 628 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 629 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 626 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 625 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 557 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 507 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 567 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 565 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 546 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 524 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 552 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 542 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 539 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 535 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 527 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 538 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 536 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 510 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 526 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 494 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 502 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 522 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 470 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 523 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 520 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 487 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 484 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 516 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 515 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 514 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 511 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 436 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 506 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 500 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 501 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 497 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 493 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 491 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 483 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 477 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 475 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 474 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 446 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 473 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 469 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 471 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 468 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 466 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 462 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 463 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 459 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 460 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 457 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 456 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 453 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 449 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 444 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 378 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 435 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 278 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 433 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 430 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 428 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 426 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 413 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 424 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 411 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 393 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 408 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 407 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 406 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 405 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 404 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 328 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 396 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 391 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 392 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 351 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 388 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 377 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 376 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 365 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 371 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 370 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 367 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 364 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 362 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 360 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 356 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 357 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 353 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 352 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 337 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 350 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 331 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 333 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 347 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 348 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 345 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 346 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 335 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 332 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 323 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 330 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 327 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 322 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 308 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 300 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 299 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 284 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 285 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 283 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 270 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 281 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 280 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 263 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 279 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 271 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 258 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 273 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 272 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 261 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 264 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 262 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 247 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 244 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 241 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 226 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 221 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 236 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 219 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 231 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 220 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 224 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: DRY RUN: Would migrate appointment ID 217 for <EMAIL>  
[2025-07-22 16:32:20] local.INFO: Clinic 14 completed: 580 processed, 0 skipped, 0 errors  
[2025-07-22 16:32:20] local.INFO: Migration completed - Processed: 580, Skipped: 0, Errors: 0  
[2025-07-22 16:32:20] local.INFO: Migration command completed successfully  
[2025-07-22 16:32:29] local.INFO: Starting migration command: migratewp:appointments  
[2025-07-22 16:32:29] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 16:32:34] local.INFO: WordPress API connection validated  
[2025-07-22 16:32:34] local.INFO: Database connection validated  
[2025-07-22 16:32:34] local.INFO: Starting appointments migration  
[2025-07-22 16:32:34] local.INFO: Starting appointments migration for clinic 14  
[2025-07-22 16:32:34] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 16:32:40] local.INFO: Found 580 appointments for clinic 14  
[2025-07-22 16:32:40] local.INFO: Clinic 14 completed: 0 processed, 580 skipped, 0 errors  
[2025-07-22 16:32:40] local.INFO: Migration completed - Processed: 0, Skipped: 580, Errors: 0  
[2025-07-22 16:32:40] local.INFO: Migration command completed successfully  
[2025-07-22 16:36:51] local.INFO: Starting migration command: migratewp:appointments  
[2025-07-22 16:36:51] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 16:36:56] local.INFO: WordPress API connection validated  
[2025-07-22 16:36:56] local.INFO: Database connection validated  
[2025-07-22 16:36:56] local.INFO: Starting appointments migration  
[2025-07-22 16:36:56] local.INFO: Running in DRY RUN mode  
[2025-07-22 16:36:56] local.INFO: Starting appointments migration for clinic 14  
[2025-07-22 16:36:56] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 16:37:02] local.INFO: Found 580 appointments for clinic 14  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1395 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1379 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1331 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1319 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1413 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1390 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1427 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1398 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1418 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1415 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1414 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1412 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1409 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1298 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1405 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1404 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1410 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1408 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1402 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1403 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1396 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1389 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1400 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1399 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1394 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1385 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1384 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1383 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1381 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1380 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1378 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1353 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1377 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1237 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1375 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1369 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1356 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1368 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1358 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1300 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1301 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1360 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1362 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1359 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1332 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1350 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1349 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1347 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1322 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1338 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1303 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1312 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1340 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1335 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1336 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1103 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1337 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1334 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1124 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1275 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1325 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1326 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1304 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1330 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1328 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1323 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1321 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1320 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1308 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1306 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1307 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1302 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1297 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1250 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1295 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1293 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1292 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1291 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1288 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1245 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1280 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1282 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1276 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1277 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1205 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1274 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1273 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1270 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1215 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1244 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1271 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1267 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1258 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1264 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1249 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1259 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1263 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1257 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1200 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1246 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1216 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1243 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1241 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1222 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1221 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1220 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1192 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1218 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1212 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1214 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1213 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1159 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1201 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1184 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1197 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1208 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1209 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1206 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1196 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1190 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1180 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1123 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1179 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1193 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1183 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1191 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1189 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1188 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1186 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1166 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1160 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1177 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1174 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1176 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1171 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1170 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1165 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1168 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1169 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1167 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1162 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1161 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1158 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1152 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1157 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1146 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1154 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1151 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1147 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1142 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1145 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1144 for peterclift123**@gmail.com  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1143 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1141 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1139 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1136 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1140 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1133 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1132 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1131 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1129 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1128 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1125 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1121 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1120 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1117 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1119 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1118 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1114 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1116 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1115 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1112 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1113 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1111 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1100 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1110 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1099 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 944 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1107 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1102 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1097 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1054 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1087 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1096 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1045 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1090 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1093 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1094 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1091 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1089 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1075 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1086 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1085 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1083 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1084 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1082 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1081 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1079 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1080 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1078 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1076 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1074 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1069 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1064 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1052 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1037 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1019 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1009 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1024 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1062 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1057 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1060 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1059 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1055 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1053 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1056 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1017 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1051 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1029 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1040 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1038 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 986 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1035 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1034 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1031 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1028 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1026 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1027 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1018 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1021 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1013 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1016 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1008 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1014 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1005 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1012 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1006 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 1004 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 999 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 995 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 996 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 994 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 993 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 992 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 955 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 987 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 981 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 984 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 985 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 961 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 978 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 980 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 976 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 968 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 970 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 969 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 966 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 941 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 948 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 965 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 935 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 887 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 956 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 848 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 940 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 953 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 950 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 881 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 902 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 942 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 938 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 936 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 937 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 915 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 933 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 934 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 932 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 874 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 891 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 931 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 928 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 913 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 929 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 927 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 926 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 920 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 924 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 914 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 921 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 919 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 908 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 912 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 905 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 897 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 896 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 904 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 853 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 899 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 850 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 893 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 892 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 864 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 890 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 889 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 888 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 886 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 885 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 884 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 882 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 877 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 883 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 829 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 880 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 879 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 830 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 858 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 875 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 876 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 838 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 873 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 872 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 871 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 870 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 869 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 868 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 862 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 861 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 860 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 859 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 856 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 854 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 852 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 844 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 795 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 847 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 841 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 843 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 839 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 837 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 836 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 835 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 834 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 833 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 775 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 831 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 832 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 828 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 784 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 827 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 826 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 825 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 824 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 813 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 819 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 816 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 810 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 814 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 812 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 811 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 806 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 805 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 802 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 727 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 799 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 797 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 793 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 714 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 791 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 792 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 789 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 777 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 787 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 743 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 783 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 781 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 779 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 778 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 741 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 772 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 774 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 770 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 702 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 771 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 769 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 768 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 742 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 740 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 732 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 739 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 726 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 729 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 719 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 639 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 551 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 683 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 677 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 707 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 712 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 709 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 711 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 693 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 710 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 708 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 706 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 705 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 704 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 694 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 696 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 692 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 697 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 666 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 691 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 688 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 689 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 686 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 685 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 653 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 679 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 665 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 667 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 668 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 661 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 657 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 656 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 654 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 652 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 651 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 650 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 633 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 644 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 649 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 646 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 648 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 642 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 618 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 641 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 632 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 640 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 540 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 568 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 638 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 525 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 637 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 634 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 635 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 628 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 629 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 626 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 625 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 557 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 507 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 567 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 565 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 546 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 524 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 552 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 542 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 539 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 535 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 527 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 538 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 536 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 510 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 526 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 494 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 502 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 522 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 470 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 523 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 520 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 487 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 484 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 516 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 515 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 514 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 511 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 436 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 506 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 500 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 501 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 497 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 493 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 491 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 483 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 477 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 475 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 474 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 446 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 473 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 469 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 471 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 468 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 466 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 462 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 463 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 459 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 460 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 457 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 456 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 453 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 449 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 444 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 378 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 435 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 278 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 433 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 430 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 428 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 426 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 413 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 424 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 411 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 393 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 408 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 407 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 406 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 405 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 404 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 328 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 396 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 391 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 392 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 351 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 388 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 377 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 376 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 365 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 371 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 370 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 367 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 364 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 362 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 360 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 356 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 357 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 353 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 352 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 337 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 350 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 331 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 333 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 347 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 348 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 345 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 346 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 335 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 332 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 323 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 330 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 327 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 322 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 308 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 300 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 299 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 284 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 285 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 283 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 270 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 281 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 280 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 263 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 279 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 271 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 258 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 273 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 272 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 261 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 264 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 262 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 247 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 244 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 241 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 226 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 221 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 236 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 219 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 231 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 220 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 224 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: DRY RUN: Would migrate appointment ID 217 for <EMAIL>  
[2025-07-22 16:37:02] local.INFO: Clinic 14 completed: 580 processed, 0 skipped, 0 errors  
[2025-07-22 16:37:02] local.INFO: Migration completed - Processed: 580, Skipped: 0, Errors: 0  
[2025-07-22 16:37:02] local.INFO: Migration command completed successfully  
[2025-07-22 16:37:15] local.INFO: Starting migration command: migratewp:appointments  
[2025-07-22 16:37:15] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 16:37:19] local.INFO: WordPress API connection validated  
[2025-07-22 16:37:19] local.INFO: Database connection validated  
[2025-07-22 16:37:19] local.INFO: Starting appointments migration  
[2025-07-22 16:37:19] local.INFO: Starting appointments migration for clinic 14  
[2025-07-22 16:37:19] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 16:37:25] local.INFO: Found 580 appointments for clinic 14  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1412 → Laravel ID 600  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1409 → Laravel ID 601  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1402 → Laravel ID 602  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1403 → Laravel ID 603  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1378 → Laravel ID 604  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1336 → Laravel ID 605  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1337 → Laravel ID 606  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1124 → Laravel ID 607  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1326 → Laravel ID 608  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1320 → Laravel ID 609  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1307 → Laravel ID 610  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1250 → Laravel ID 611  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1295 → Laravel ID 612  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1258 → Laravel ID 613  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1263 → Laravel ID 614  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1257 → Laravel ID 615  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1216 → Laravel ID 616  
[2025-07-22 16:37:25] local.INFO: Successfully migrated appointment ID 1201 → Laravel ID 617  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1209 → Laravel ID 618  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1123 → Laravel ID 619  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1179 → Laravel ID 620  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1183 → Laravel ID 621  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1188 → Laravel ID 622  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1186 → Laravel ID 623  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1177 → Laravel ID 624  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1136 → Laravel ID 625  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1133 → Laravel ID 626  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1120 → Laravel ID 627  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1117 → Laravel ID 628  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1119 → Laravel ID 629  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1114 → Laravel ID 630  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1116 → Laravel ID 631  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 944 → Laravel ID 632  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1102 → Laravel ID 633  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1054 → Laravel ID 634  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1087 → Laravel ID 635  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1045 → Laravel ID 636  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1093 → Laravel ID 637  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1094 → Laravel ID 638  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1089 → Laravel ID 639  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1085 → Laravel ID 640  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1083 → Laravel ID 641  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1078 → Laravel ID 642  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 986 → Laravel ID 643  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1034 → Laravel ID 644  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1031 → Laravel ID 645  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1021 → Laravel ID 646  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 1008 → Laravel ID 647  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 996 → Laravel ID 648  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 955 → Laravel ID 649  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 987 → Laravel ID 650  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 985 → Laravel ID 651  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 980 → Laravel ID 652  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 941 → Laravel ID 653  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 935 → Laravel ID 654  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 881 → Laravel ID 655  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 915 → Laravel ID 656  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 891 → Laravel ID 657  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 927 → Laravel ID 658  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 919 → Laravel ID 659  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 908 → Laravel ID 660  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 912 → Laravel ID 661  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 896 → Laravel ID 662  
[2025-07-22 16:37:26] local.INFO: Successfully migrated appointment ID 893 → Laravel ID 663  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 889 → Laravel ID 664  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 884 → Laravel ID 665  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 877 → Laravel ID 666  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 883 → Laravel ID 667  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 879 → Laravel ID 668  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 830 → Laravel ID 669  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 858 → Laravel ID 670  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 869 → Laravel ID 671  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 862 → Laravel ID 672  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 861 → Laravel ID 673  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 859 → Laravel ID 674  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 841 → Laravel ID 675  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 833 → Laravel ID 676  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 827 → Laravel ID 677  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 826 → Laravel ID 678  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 825 → Laravel ID 679  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 810 → Laravel ID 680  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 806 → Laravel ID 681  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 802 → Laravel ID 682  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 787 → Laravel ID 683  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 743 → Laravel ID 684  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 742 → Laravel ID 685  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 729 → Laravel ID 686  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 719 → Laravel ID 687  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 677 → Laravel ID 688  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 707 → Laravel ID 689  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 711 → Laravel ID 690  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 696 → Laravel ID 691  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 688 → Laravel ID 692  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 654 → Laravel ID 693  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 525 → Laravel ID 694  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 635 → Laravel ID 695  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 557 → Laravel ID 696  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 524 → Laravel ID 697  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 552 → Laravel ID 698  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 535 → Laravel ID 699  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 536 → Laravel ID 700  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 510 → Laravel ID 701  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 502 → Laravel ID 702  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 523 → Laravel ID 703  
[2025-07-22 16:37:27] local.INFO: Successfully migrated appointment ID 516 → Laravel ID 704  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 506 → Laravel ID 705  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 500 → Laravel ID 706  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 501 → Laravel ID 707  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 471 → Laravel ID 708  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 468 → Laravel ID 709  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 466 → Laravel ID 710  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 462 → Laravel ID 711  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 460 → Laravel ID 712  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 457 → Laravel ID 713  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 456 → Laravel ID 714  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 444 → Laravel ID 715  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 378 → Laravel ID 716  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 430 → Laravel ID 717  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 428 → Laravel ID 718  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 424 → Laravel ID 719  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 393 → Laravel ID 720  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 408 → Laravel ID 721  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 405 → Laravel ID 722  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 404 → Laravel ID 723  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 328 → Laravel ID 724  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 392 → Laravel ID 725  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 377 → Laravel ID 726  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 376 → Laravel ID 727  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 370 → Laravel ID 728  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 367 → Laravel ID 729  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 356 → Laravel ID 730  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 353 → Laravel ID 731  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 331 → Laravel ID 732  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 333 → Laravel ID 733  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 330 → Laravel ID 734  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 327 → Laravel ID 735  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 322 → Laravel ID 736  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 299 → Laravel ID 737  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 285 → Laravel ID 738  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 270 → Laravel ID 739  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 271 → Laravel ID 740  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 262 → Laravel ID 741  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 241 → Laravel ID 742  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 236 → Laravel ID 743  
[2025-07-22 16:37:28] local.INFO: Successfully migrated appointment ID 224 → Laravel ID 744  
[2025-07-22 16:37:28] local.INFO: Clinic 14 completed: 145 processed, 0 skipped, 435 errors  
[2025-07-22 16:37:28] local.INFO: Migration completed - Processed: 145, Skipped: 0, Errors: 435  
[2025-07-22 16:37:28] local.INFO: Migration command completed successfully  
[2025-07-22 16:52:12] local.INFO: Starting migration command: migratewp:appointments  
[2025-07-22 16:52:12] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 16:52:18] local.INFO: WordPress API connection validated  
[2025-07-22 16:52:18] local.INFO: Database connection validated  
[2025-07-22 16:52:18] local.INFO: Starting appointments migration  
[2025-07-22 16:52:18] local.INFO: Starting appointments migration for clinic 14  
[2025-07-22 16:52:18] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 16:52:23] local.INFO: Found 580 appointments for clinic 14  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1395 → Laravel ID 745  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1379 → Laravel ID 746  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1331 → Laravel ID 747  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1319 → Laravel ID 748  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1413 → Laravel ID 749  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1390 → Laravel ID 750  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1427 → Laravel ID 751  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1398 → Laravel ID 752  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1418 → Laravel ID 753  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1415 → Laravel ID 754  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1414 → Laravel ID 755  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1298 → Laravel ID 756  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1405 → Laravel ID 757  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1404 → Laravel ID 758  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1410 → Laravel ID 759  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1408 → Laravel ID 760  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1396 → Laravel ID 761  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1389 → Laravel ID 762  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1400 → Laravel ID 763  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1399 → Laravel ID 764  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1394 → Laravel ID 765  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1385 → Laravel ID 766  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1384 → Laravel ID 767  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1383 → Laravel ID 768  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1381 → Laravel ID 769  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1380 → Laravel ID 770  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1353 → Laravel ID 771  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1377 → Laravel ID 772  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1237 → Laravel ID 773  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1375 → Laravel ID 774  
[2025-07-22 16:52:23] local.INFO: Successfully migrated appointment ID 1369 → Laravel ID 775  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1356 → Laravel ID 776  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1368 → Laravel ID 777  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1358 → Laravel ID 778  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1300 → Laravel ID 779  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1301 → Laravel ID 780  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1360 → Laravel ID 781  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1362 → Laravel ID 782  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1359 → Laravel ID 783  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1332 → Laravel ID 784  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1350 → Laravel ID 785  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1349 → Laravel ID 786  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1347 → Laravel ID 787  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1322 → Laravel ID 788  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1338 → Laravel ID 789  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1303 → Laravel ID 790  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1312 → Laravel ID 791  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1340 → Laravel ID 792  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1335 → Laravel ID 793  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1103 → Laravel ID 794  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1334 → Laravel ID 795  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1275 → Laravel ID 796  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1325 → Laravel ID 797  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1304 → Laravel ID 798  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1330 → Laravel ID 799  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1328 → Laravel ID 800  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1323 → Laravel ID 801  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1321 → Laravel ID 802  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1308 → Laravel ID 803  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1306 → Laravel ID 804  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1302 → Laravel ID 805  
[2025-07-22 16:52:24] local.INFO: Successfully migrated appointment ID 1297 → Laravel ID 806  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1293 → Laravel ID 807  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1292 → Laravel ID 808  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1291 → Laravel ID 809  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1288 → Laravel ID 810  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1245 → Laravel ID 811  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1280 → Laravel ID 812  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1282 → Laravel ID 813  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1276 → Laravel ID 814  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1277 → Laravel ID 815  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1205 → Laravel ID 816  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1274 → Laravel ID 817  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1273 → Laravel ID 818  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1270 → Laravel ID 819  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1215 → Laravel ID 820  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1244 → Laravel ID 821  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1271 → Laravel ID 822  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1267 → Laravel ID 823  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1264 → Laravel ID 824  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1249 → Laravel ID 825  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1259 → Laravel ID 826  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1200 → Laravel ID 827  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1246 → Laravel ID 828  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1243 → Laravel ID 829  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1241 → Laravel ID 830  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1222 → Laravel ID 831  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1221 → Laravel ID 832  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1220 → Laravel ID 833  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1192 → Laravel ID 834  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1218 → Laravel ID 835  
[2025-07-22 16:52:25] local.INFO: Successfully migrated appointment ID 1212 → Laravel ID 836  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1214 → Laravel ID 837  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1213 → Laravel ID 838  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1159 → Laravel ID 839  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1184 → Laravel ID 840  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1197 → Laravel ID 841  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1208 → Laravel ID 842  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1206 → Laravel ID 843  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1196 → Laravel ID 844  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1190 → Laravel ID 845  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1180 → Laravel ID 846  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1193 → Laravel ID 847  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1191 → Laravel ID 848  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1189 → Laravel ID 849  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1166 → Laravel ID 850  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1160 → Laravel ID 851  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1174 → Laravel ID 852  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1176 → Laravel ID 853  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1171 → Laravel ID 854  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1170 → Laravel ID 855  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1165 → Laravel ID 856  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1168 → Laravel ID 857  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1169 → Laravel ID 858  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1167 → Laravel ID 859  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1162 → Laravel ID 860  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1161 → Laravel ID 861  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1158 → Laravel ID 862  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1152 → Laravel ID 863  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1157 → Laravel ID 864  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1146 → Laravel ID 865  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1154 → Laravel ID 866  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1151 → Laravel ID 867  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1147 → Laravel ID 868  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1142 → Laravel ID 869  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1145 → Laravel ID 870  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1144 → Laravel ID 871  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1143 → Laravel ID 872  
[2025-07-22 16:52:26] local.INFO: Successfully migrated appointment ID 1141 → Laravel ID 873  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1139 → Laravel ID 874  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1140 → Laravel ID 875  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1132 → Laravel ID 876  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1131 → Laravel ID 877  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1129 → Laravel ID 878  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1128 → Laravel ID 879  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1125 → Laravel ID 880  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1121 → Laravel ID 881  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1118 → Laravel ID 882  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1115 → Laravel ID 883  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1112 → Laravel ID 884  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1113 → Laravel ID 885  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1111 → Laravel ID 886  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1100 → Laravel ID 887  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1110 → Laravel ID 888  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1099 → Laravel ID 889  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1107 → Laravel ID 890  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1097 → Laravel ID 891  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1096 → Laravel ID 892  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1090 → Laravel ID 893  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1091 → Laravel ID 894  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1075 → Laravel ID 895  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1086 → Laravel ID 896  
[2025-07-22 16:52:27] local.INFO: Successfully migrated appointment ID 1084 → Laravel ID 897  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1082 → Laravel ID 898  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1081 → Laravel ID 899  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1079 → Laravel ID 900  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1080 → Laravel ID 901  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1076 → Laravel ID 902  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1074 → Laravel ID 903  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1069 → Laravel ID 904  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1064 → Laravel ID 905  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1052 → Laravel ID 906  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1037 → Laravel ID 907  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1019 → Laravel ID 908  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1009 → Laravel ID 909  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1024 → Laravel ID 910  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1062 → Laravel ID 911  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1057 → Laravel ID 912  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1060 → Laravel ID 913  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1059 → Laravel ID 914  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1055 → Laravel ID 915  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1053 → Laravel ID 916  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1056 → Laravel ID 917  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1017 → Laravel ID 918  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1051 → Laravel ID 919  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1029 → Laravel ID 920  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1040 → Laravel ID 921  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1038 → Laravel ID 922  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1035 → Laravel ID 923  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1028 → Laravel ID 924  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1026 → Laravel ID 925  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1027 → Laravel ID 926  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1018 → Laravel ID 927  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1013 → Laravel ID 928  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1016 → Laravel ID 929  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1014 → Laravel ID 930  
[2025-07-22 16:52:28] local.INFO: Successfully migrated appointment ID 1005 → Laravel ID 931  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 1012 → Laravel ID 932  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 1006 → Laravel ID 933  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 1004 → Laravel ID 934  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 999 → Laravel ID 935  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 995 → Laravel ID 936  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 994 → Laravel ID 937  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 993 → Laravel ID 938  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 992 → Laravel ID 939  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 981 → Laravel ID 940  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 984 → Laravel ID 941  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 961 → Laravel ID 942  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 978 → Laravel ID 943  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 976 → Laravel ID 944  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 968 → Laravel ID 945  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 970 → Laravel ID 946  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 969 → Laravel ID 947  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 966 → Laravel ID 948  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 948 → Laravel ID 949  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 965 → Laravel ID 950  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 887 → Laravel ID 951  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 956 → Laravel ID 952  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 848 → Laravel ID 953  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 940 → Laravel ID 954  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 953 → Laravel ID 955  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 950 → Laravel ID 956  
[2025-07-22 16:52:29] local.INFO: Successfully migrated appointment ID 902 → Laravel ID 957  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 942 → Laravel ID 958  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 938 → Laravel ID 959  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 936 → Laravel ID 960  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 937 → Laravel ID 961  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 933 → Laravel ID 962  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 934 → Laravel ID 963  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 932 → Laravel ID 964  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 874 → Laravel ID 965  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 931 → Laravel ID 966  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 928 → Laravel ID 967  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 913 → Laravel ID 968  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 929 → Laravel ID 969  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 926 → Laravel ID 970  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 920 → Laravel ID 971  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 924 → Laravel ID 972  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 914 → Laravel ID 973  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 921 → Laravel ID 974  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 905 → Laravel ID 975  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 897 → Laravel ID 976  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 904 → Laravel ID 977  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 853 → Laravel ID 978  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 899 → Laravel ID 979  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 850 → Laravel ID 980  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 892 → Laravel ID 981  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 864 → Laravel ID 982  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 890 → Laravel ID 983  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 888 → Laravel ID 984  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 886 → Laravel ID 985  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 885 → Laravel ID 986  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 882 → Laravel ID 987  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 829 → Laravel ID 988  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 880 → Laravel ID 989  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 875 → Laravel ID 990  
[2025-07-22 16:52:30] local.INFO: Successfully migrated appointment ID 876 → Laravel ID 991  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 838 → Laravel ID 992  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 873 → Laravel ID 993  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 872 → Laravel ID 994  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 871 → Laravel ID 995  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 870 → Laravel ID 996  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 868 → Laravel ID 997  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 860 → Laravel ID 998  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 856 → Laravel ID 999  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 854 → Laravel ID 1000  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 852 → Laravel ID 1001  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 844 → Laravel ID 1002  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 795 → Laravel ID 1003  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 847 → Laravel ID 1004  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 843 → Laravel ID 1005  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 839 → Laravel ID 1006  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 837 → Laravel ID 1007  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 836 → Laravel ID 1008  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 835 → Laravel ID 1009  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 834 → Laravel ID 1010  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 775 → Laravel ID 1011  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 831 → Laravel ID 1012  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 832 → Laravel ID 1013  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 828 → Laravel ID 1014  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 784 → Laravel ID 1015  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 824 → Laravel ID 1016  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 813 → Laravel ID 1017  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 819 → Laravel ID 1018  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 816 → Laravel ID 1019  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 814 → Laravel ID 1020  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 812 → Laravel ID 1021  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 811 → Laravel ID 1022  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 805 → Laravel ID 1023  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 727 → Laravel ID 1024  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 799 → Laravel ID 1025  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 797 → Laravel ID 1026  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 793 → Laravel ID 1027  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 714 → Laravel ID 1028  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 791 → Laravel ID 1029  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 792 → Laravel ID 1030  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 789 → Laravel ID 1031  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 777 → Laravel ID 1032  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 783 → Laravel ID 1033  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 781 → Laravel ID 1034  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 779 → Laravel ID 1035  
[2025-07-22 16:52:31] local.INFO: Successfully migrated appointment ID 778 → Laravel ID 1036  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 741 → Laravel ID 1037  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 772 → Laravel ID 1038  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 774 → Laravel ID 1039  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 770 → Laravel ID 1040  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 702 → Laravel ID 1041  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 771 → Laravel ID 1042  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 769 → Laravel ID 1043  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 768 → Laravel ID 1044  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 740 → Laravel ID 1045  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 732 → Laravel ID 1046  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 739 → Laravel ID 1047  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 726 → Laravel ID 1048  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 639 → Laravel ID 1049  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 551 → Laravel ID 1050  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 683 → Laravel ID 1051  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 712 → Laravel ID 1052  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 709 → Laravel ID 1053  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 693 → Laravel ID 1054  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 710 → Laravel ID 1055  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 708 → Laravel ID 1056  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 706 → Laravel ID 1057  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 705 → Laravel ID 1058  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 704 → Laravel ID 1059  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 694 → Laravel ID 1060  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 692 → Laravel ID 1061  
[2025-07-22 16:52:32] local.INFO: Successfully migrated appointment ID 697 → Laravel ID 1062  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 666 → Laravel ID 1063  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 691 → Laravel ID 1064  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 689 → Laravel ID 1065  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 686 → Laravel ID 1066  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 685 → Laravel ID 1067  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 653 → Laravel ID 1068  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 679 → Laravel ID 1069  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 665 → Laravel ID 1070  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 667 → Laravel ID 1071  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 668 → Laravel ID 1072  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 661 → Laravel ID 1073  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 657 → Laravel ID 1074  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 656 → Laravel ID 1075  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 652 → Laravel ID 1076  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 651 → Laravel ID 1077  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 650 → Laravel ID 1078  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 633 → Laravel ID 1079  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 644 → Laravel ID 1080  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 649 → Laravel ID 1081  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 646 → Laravel ID 1082  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 648 → Laravel ID 1083  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 642 → Laravel ID 1084  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 618 → Laravel ID 1085  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 641 → Laravel ID 1086  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 632 → Laravel ID 1087  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 640 → Laravel ID 1088  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 540 → Laravel ID 1089  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 568 → Laravel ID 1090  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 638 → Laravel ID 1091  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 637 → Laravel ID 1092  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 634 → Laravel ID 1093  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 628 → Laravel ID 1094  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 629 → Laravel ID 1095  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 626 → Laravel ID 1096  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 625 → Laravel ID 1097  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 507 → Laravel ID 1098  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 567 → Laravel ID 1099  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 565 → Laravel ID 1100  
[2025-07-22 16:52:33] local.INFO: Successfully migrated appointment ID 546 → Laravel ID 1101  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 542 → Laravel ID 1102  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 539 → Laravel ID 1103  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 527 → Laravel ID 1104  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 538 → Laravel ID 1105  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 526 → Laravel ID 1106  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 494 → Laravel ID 1107  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 522 → Laravel ID 1108  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 470 → Laravel ID 1109  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 520 → Laravel ID 1110  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 487 → Laravel ID 1111  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 484 → Laravel ID 1112  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 515 → Laravel ID 1113  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 514 → Laravel ID 1114  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 511 → Laravel ID 1115  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 436 → Laravel ID 1116  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 497 → Laravel ID 1117  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 493 → Laravel ID 1118  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 491 → Laravel ID 1119  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 483 → Laravel ID 1120  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 477 → Laravel ID 1121  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 475 → Laravel ID 1122  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 474 → Laravel ID 1123  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 446 → Laravel ID 1124  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 473 → Laravel ID 1125  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 469 → Laravel ID 1126  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 463 → Laravel ID 1127  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 459 → Laravel ID 1128  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 453 → Laravel ID 1129  
[2025-07-22 16:52:34] local.INFO: Successfully migrated appointment ID 449 → Laravel ID 1130  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 435 → Laravel ID 1131  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 278 → Laravel ID 1132  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 433 → Laravel ID 1133  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 426 → Laravel ID 1134  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 413 → Laravel ID 1135  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 411 → Laravel ID 1136  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 407 → Laravel ID 1137  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 406 → Laravel ID 1138  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 396 → Laravel ID 1139  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 391 → Laravel ID 1140  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 351 → Laravel ID 1141  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 388 → Laravel ID 1142  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 365 → Laravel ID 1143  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 371 → Laravel ID 1144  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 364 → Laravel ID 1145  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 362 → Laravel ID 1146  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 360 → Laravel ID 1147  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 357 → Laravel ID 1148  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 352 → Laravel ID 1149  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 337 → Laravel ID 1150  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 350 → Laravel ID 1151  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 347 → Laravel ID 1152  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 348 → Laravel ID 1153  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 345 → Laravel ID 1154  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 346 → Laravel ID 1155  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 335 → Laravel ID 1156  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 332 → Laravel ID 1157  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 323 → Laravel ID 1158  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 308 → Laravel ID 1159  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 300 → Laravel ID 1160  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 284 → Laravel ID 1161  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 283 → Laravel ID 1162  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 281 → Laravel ID 1163  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 280 → Laravel ID 1164  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 263 → Laravel ID 1165  
[2025-07-22 16:52:35] local.INFO: Successfully migrated appointment ID 279 → Laravel ID 1166  
[2025-07-22 16:52:36] local.INFO: Successfully migrated appointment ID 258 → Laravel ID 1167  
[2025-07-22 16:52:36] local.INFO: Successfully migrated appointment ID 273 → Laravel ID 1168  
[2025-07-22 16:52:36] local.INFO: Successfully migrated appointment ID 272 → Laravel ID 1169  
[2025-07-22 16:52:36] local.INFO: Successfully migrated appointment ID 261 → Laravel ID 1170  
[2025-07-22 16:52:36] local.INFO: Successfully migrated appointment ID 264 → Laravel ID 1171  
[2025-07-22 16:52:36] local.INFO: Successfully migrated appointment ID 247 → Laravel ID 1172  
[2025-07-22 16:52:36] local.INFO: Successfully migrated appointment ID 244 → Laravel ID 1173  
[2025-07-22 16:52:36] local.INFO: Successfully migrated appointment ID 226 → Laravel ID 1174  
[2025-07-22 16:52:36] local.INFO: Successfully migrated appointment ID 221 → Laravel ID 1175  
[2025-07-22 16:52:36] local.INFO: Successfully migrated appointment ID 219 → Laravel ID 1176  
[2025-07-22 16:52:36] local.INFO: Successfully migrated appointment ID 231 → Laravel ID 1177  
[2025-07-22 16:52:36] local.INFO: Successfully migrated appointment ID 220 → Laravel ID 1178  
[2025-07-22 16:52:36] local.INFO: Successfully migrated appointment ID 217 → Laravel ID 1179  
[2025-07-22 16:52:36] local.INFO: Clinic 14 completed: 435 processed, 0 skipped, 145 errors  
[2025-07-22 16:52:36] local.INFO: Migration completed - Processed: 435, Skipped: 0, Errors: 145  
[2025-07-22 16:52:36] local.INFO: Migration command completed successfully  
