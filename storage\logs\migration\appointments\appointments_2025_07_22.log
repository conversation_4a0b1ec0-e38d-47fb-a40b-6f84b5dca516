[2025-07-22 15:55:35] local.INFO: Starting migration command: migratewp:appointments  
[2025-07-22 15:55:35] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 15:55:42] local.INFO: WordPress API connection validated  
[2025-07-22 15:55:42] local.INFO: Database connection validated  
[2025-07-22 15:55:42] local.INFO: Starting appointments migration  
[2025-07-22 15:55:42] local.INFO: Running in DRY RUN mode  
[2025-07-22 15:55:42] local.INFO: Making KiviCare API request: laravel_get_clinics  
[2025-07-22 15:55:46] local.INFO: Starting appointments migration for clinic 40  
[2025-07-22 15:55:46] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:55:51] local.INFO: Found 5 appointments for clinic 40  
[2025-07-22 15:55:51] local.INFO: DRY RUN: Would migrate appointment ID 1050 for   
[2025-07-22 15:55:51] local.INFO: DRY RUN: Would migrate appointment ID 1002 for   
[2025-07-22 15:55:51] local.INFO: DRY RUN: Would migrate appointment ID 958 for   
[2025-07-22 15:55:51] local.INFO: DRY RUN: Would migrate appointment ID 960 for   
[2025-07-22 15:55:51] local.INFO: DRY RUN: Would migrate appointment ID 959 for   
[2025-07-22 15:55:51] local.INFO: Clinic 40 completed: 5 processed, 0 skipped, 0 errors  
[2025-07-22 15:55:51] local.INFO: Starting appointments migration for clinic 27  
[2025-07-22 15:55:51] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:55:56] local.INFO: No appointments found for clinic 27  
[2025-07-22 15:55:56] local.INFO: Starting appointments migration for clinic 26  
[2025-07-22 15:55:56] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:02] local.INFO: No appointments found for clinic 26  
[2025-07-22 15:56:02] local.INFO: Starting appointments migration for clinic 22  
[2025-07-22 15:56:02] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:06] local.INFO: No appointments found for clinic 22  
[2025-07-22 15:56:06] local.INFO: Starting appointments migration for clinic 16  
[2025-07-22 15:56:06] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:09] local.INFO: Found 17 appointments for clinic 16  
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 1431 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 1432 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 1416 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 911 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 863 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 851 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 807 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 803 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 680 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 631 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 617 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 559 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 550 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 553 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 543 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 402 for   
[2025-07-22 15:56:09] local.INFO: DRY RUN: Would migrate appointment ID 398 for   
[2025-07-22 15:56:09] local.INFO: Clinic 16 completed: 17 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:09] local.INFO: Starting appointments migration for clinic 15  
[2025-07-22 15:56:09] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:13] local.INFO: No appointments found for clinic 15  
[2025-07-22 15:56:13] local.INFO: Starting appointments migration for clinic 14  
[2025-07-22 15:56:13] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:18] local.INFO: Found 580 appointments for clinic 14  
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1395 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1379 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1331 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1319 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1413 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1390 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1427 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1398 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1418 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1415 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1414 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1412 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1409 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1298 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1405 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1404 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1410 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1408 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1402 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1403 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1396 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1389 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1400 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1399 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1394 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1385 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1384 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1383 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1381 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1380 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1378 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1353 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1377 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1237 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1375 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1369 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1356 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1368 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1358 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1300 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1301 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1360 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1362 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1359 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1332 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1350 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1349 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1347 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1322 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1338 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1303 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1312 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1340 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1335 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1336 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1103 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1337 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1334 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1124 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1275 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1325 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1326 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1304 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1330 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1328 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1323 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1321 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1320 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1308 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1306 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1307 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1302 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1297 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1250 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1295 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1293 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1292 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1291 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1288 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1245 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1280 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1282 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1276 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1277 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1205 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1274 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1273 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1270 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1215 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1244 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1271 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1267 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1258 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1264 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1249 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1259 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1263 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1257 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1200 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1246 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1216 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1243 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1241 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1222 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1221 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1220 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1192 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1218 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1212 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1214 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1213 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1159 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1201 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1184 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1197 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1208 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1209 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1206 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1196 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1190 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1180 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1123 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1179 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1193 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1183 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1191 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1189 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1188 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1186 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1166 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1160 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1177 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1174 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1176 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1171 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1170 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1165 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1168 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1169 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1167 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1162 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1161 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1158 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1152 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1157 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1146 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1154 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1151 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1147 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1142 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1145 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1144 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1143 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1141 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1139 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1136 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1140 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1133 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1132 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1131 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1129 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1128 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1125 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1121 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1120 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1117 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1119 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1118 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1114 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1116 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1115 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1112 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1113 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1111 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1100 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1110 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1099 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 944 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1107 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1102 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1097 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1054 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1087 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1096 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1045 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1090 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1093 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1094 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1091 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1089 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1075 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1086 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1085 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1083 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1084 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1082 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1081 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1079 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1080 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1078 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1076 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1074 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1069 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1064 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1052 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1037 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1019 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1009 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1024 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1062 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1057 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1060 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1059 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1055 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1053 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1056 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1017 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1051 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1029 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1040 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1038 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 986 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1035 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1034 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1031 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1028 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1026 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1027 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1018 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1021 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1013 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1016 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1008 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1014 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1005 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1012 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1006 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 1004 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 999 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 995 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 996 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 994 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 993 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 992 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 955 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 987 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 981 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 984 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 985 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 961 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 978 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 980 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 976 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 968 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 970 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 969 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 966 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 941 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 948 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 965 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 935 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 887 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 956 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 848 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 940 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 953 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 950 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 881 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 902 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 942 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 938 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 936 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 937 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 915 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 933 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 934 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 932 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 874 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 891 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 931 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 928 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 913 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 929 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 927 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 926 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 920 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 924 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 914 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 921 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 919 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 908 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 912 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 905 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 897 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 896 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 904 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 853 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 899 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 850 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 893 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 892 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 864 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 890 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 889 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 888 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 886 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 885 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 884 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 882 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 877 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 883 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 829 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 880 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 879 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 830 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 858 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 875 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 876 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 838 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 873 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 872 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 871 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 870 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 869 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 868 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 862 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 861 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 860 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 859 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 856 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 854 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 852 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 844 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 795 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 847 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 841 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 843 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 839 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 837 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 836 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 835 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 834 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 833 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 775 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 831 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 832 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 828 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 784 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 827 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 826 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 825 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 824 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 813 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 819 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 816 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 810 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 814 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 812 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 811 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 806 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 805 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 802 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 727 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 799 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 797 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 793 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 714 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 791 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 792 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 789 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 777 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 787 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 743 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 783 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 781 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 779 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 778 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 741 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 772 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 774 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 770 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 702 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 771 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 769 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 768 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 742 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 740 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 732 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 739 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 726 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 729 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 719 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 639 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 551 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 683 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 677 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 707 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 712 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 709 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 711 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 693 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 710 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 708 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 706 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 705 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 704 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 694 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 696 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 692 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 697 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 666 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 691 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 688 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 689 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 686 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 685 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 653 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 679 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 665 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 667 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 668 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 661 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 657 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 656 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 654 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 652 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 651 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 650 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 633 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 644 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 649 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 646 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 648 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 642 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 618 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 641 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 632 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 640 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 540 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 568 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 638 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 525 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 637 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 634 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 635 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 628 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 629 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 626 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 625 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 557 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 507 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 567 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 565 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 546 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 524 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 552 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 542 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 539 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 535 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 527 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 538 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 536 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 510 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 526 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 494 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 502 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 522 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 470 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 523 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 520 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 487 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 484 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 516 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 515 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 514 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 511 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 436 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 506 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 500 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 501 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 497 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 493 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 491 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 483 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 477 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 475 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 474 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 446 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 473 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 469 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 471 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 468 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 466 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 462 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 463 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 459 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 460 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 457 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 456 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 453 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 449 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 444 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 378 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 435 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 278 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 433 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 430 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 428 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 426 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 413 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 424 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 411 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 393 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 408 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 407 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 406 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 405 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 404 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 328 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 396 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 391 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 392 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 351 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 388 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 377 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 376 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 365 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 371 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 370 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 367 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 364 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 362 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 360 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 356 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 357 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 353 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 352 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 337 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 350 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 331 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 333 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 347 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 348 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 345 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 346 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 335 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 332 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 323 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 330 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 327 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 322 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 308 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 300 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 299 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 284 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 285 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 283 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 270 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 281 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 280 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 263 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 279 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 271 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 258 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 273 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 272 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 261 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 264 for   
[2025-07-22 15:56:18] local.INFO: DRY RUN: Would migrate appointment ID 262 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 247 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 244 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 241 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 226 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 221 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 236 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 219 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 231 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 220 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 224 for   
[2025-07-22 15:56:19] local.INFO: DRY RUN: Would migrate appointment ID 217 for   
[2025-07-22 15:56:19] local.INFO: Clinic 14 completed: 580 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:19] local.INFO: Starting appointments migration for clinic 13  
[2025-07-22 15:56:19] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:22] local.INFO: Found 1 appointments for clinic 13  
[2025-07-22 15:56:22] local.INFO: DRY RUN: Would migrate appointment ID 81 for   
[2025-07-22 15:56:22] local.INFO: Clinic 13 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:22] local.INFO: Starting appointments migration for clinic 12  
[2025-07-22 15:56:22] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:25] local.INFO: Found 22 appointments for clinic 12  
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1374 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1371 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1370 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1345 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1313 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1279 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1242 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1239 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1149 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1036 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 1023 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 997 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 952 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 962 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 951 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 643 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 623 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 548 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 519 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 455 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 401 for   
[2025-07-22 15:56:25] local.INFO: DRY RUN: Would migrate appointment ID 338 for   
[2025-07-22 15:56:25] local.INFO: Clinic 12 completed: 22 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:25] local.INFO: Starting appointments migration for clinic 11  
[2025-07-22 15:56:25] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:29] local.INFO: Found 305 appointments for clinic 11  
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1406 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1397 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1388 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1382 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1365 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1372 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1367 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1364 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1363 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1361 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1354 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1352 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1348 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1324 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1344 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1343 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1329 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1333 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1314 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1309 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1299 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1289 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1290 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1284 for   
[2025-07-22 15:56:29] local.INFO: DRY RUN: Would migrate appointment ID 1285 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1281 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1287 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1286 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1278 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1272 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1268 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1265 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1266 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1256 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1253 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1254 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1248 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1207 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1219 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1217 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1240 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1235 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1234 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1224 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1233 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1211 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1198 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1199 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1194 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1185 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1163 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1155 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1156 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1181 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1182 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1172 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1134 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1138 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1135 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1127 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1126 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1108 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1105 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1101 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1104 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1095 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1092 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1088 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1070 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1071 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1068 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1061 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1048 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1049 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1033 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1030 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1025 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1020 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1010 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1007 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 1003 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 998 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 991 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 990 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 989 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 988 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 971 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 967 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 954 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 918 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 946 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 945 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 939 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 930 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 925 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 922 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 923 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 878 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 909 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 910 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 916 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 907 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 867 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 840 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 738 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 809 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 808 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 801 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 798 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 796 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 794 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 788 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 737 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 776 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 725 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 767 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 718 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 730 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 721 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 717 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 564 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 715 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 695 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 687 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 684 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 678 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 681 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 682 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 675 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 669 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 664 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 660 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 663 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 636 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 569 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 566 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 563 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 556 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 531 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 532 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 541 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 530 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 495 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 496 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 505 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 529 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 513 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 512 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 504 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 489 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 488 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 479 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 482 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 480 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 481 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 465 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 454 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 432 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 472 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 464 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 461 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 458 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 450 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 442 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 443 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 434 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 439 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 437 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 431 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 429 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 427 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 409 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 421 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 399 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 394 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 395 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 390 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 389 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 379 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 369 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 374 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 372 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 341 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 349 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 344 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 334 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 343 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 340 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 317 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 324 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 316 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 314 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 306 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 310 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 304 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 298 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 295 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 282 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 294 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 277 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 268 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 269 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 266 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 260 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 259 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 257 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 255 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 249 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 248 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 242 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 240 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 235 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 234 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 233 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 232 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 228 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 225 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 227 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 222 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 223 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 215 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 214 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 209 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 182 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 176 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 194 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 180 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 178 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 179 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 173 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 171 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 165 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 164 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 163 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 158 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 150 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 154 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 153 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 147 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 144 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 132 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 141 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 142 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 143 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 120 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 121 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 134 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 131 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 139 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 130 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 129 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 127 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 125 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 126 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 118 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 124 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 115 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 123 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 122 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 116 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 119 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 112 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 113 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 114 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 111 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 110 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 109 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 108 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 105 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 107 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 104 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 103 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 99 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 97 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 98 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 96 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 95 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 93 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 92 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 89 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 90 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 91 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 88 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 87 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 86 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 83 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 84 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 85 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 77 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 76 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 73 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 72 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 71 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 70 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 67 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 66 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 64 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 63 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 17 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 23 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 24 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 19 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 15 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 14 for   
[2025-07-22 15:56:30] local.INFO: DRY RUN: Would migrate appointment ID 13 for   
[2025-07-22 15:56:30] local.INFO: Clinic 11 completed: 305 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:30] local.INFO: Starting appointments migration for clinic 10  
[2025-07-22 15:56:30] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:33] local.INFO: Found 1 appointments for clinic 10  
[2025-07-22 15:56:33] local.INFO: DRY RUN: Would migrate appointment ID 136 for   
[2025-07-22 15:56:33] local.INFO: Clinic 10 completed: 1 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:33] local.INFO: Starting appointments migration for clinic 9  
[2025-07-22 15:56:33] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:37] local.INFO: Found 2 appointments for clinic 9  
[2025-07-22 15:56:37] local.INFO: DRY RUN: Would migrate appointment ID 62 for   
[2025-07-22 15:56:37] local.INFO: DRY RUN: Would migrate appointment ID 61 for   
[2025-07-22 15:56:37] local.INFO: Clinic 9 completed: 2 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:37] local.INFO: Starting appointments migration for clinic 8  
[2025-07-22 15:56:37] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:41] local.INFO: Found 7 appointments for clinic 8  
[2025-07-22 15:56:41] local.INFO: DRY RUN: Would migrate appointment ID 658 for   
[2025-07-22 15:56:41] local.INFO: DRY RUN: Would migrate appointment ID 616 for   
[2025-07-22 15:56:41] local.INFO: DRY RUN: Would migrate appointment ID 410 for   
[2025-07-22 15:56:41] local.INFO: DRY RUN: Would migrate appointment ID 326 for   
[2025-07-22 15:56:41] local.INFO: DRY RUN: Would migrate appointment ID 265 for   
[2025-07-22 15:56:41] local.INFO: DRY RUN: Would migrate appointment ID 146 for   
[2025-07-22 15:56:41] local.INFO: DRY RUN: Would migrate appointment ID 100 for   
[2025-07-22 15:56:41] local.INFO: Clinic 8 completed: 7 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:41] local.INFO: Starting appointments migration for clinic 6  
[2025-07-22 15:56:41] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:45] local.INFO: No appointments found for clinic 6  
[2025-07-22 15:56:45] local.INFO: Starting appointments migration for clinic 5  
[2025-07-22 15:56:45] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:50] local.INFO: Found 38 appointments for clinic 5  
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1148 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1341 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1260 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1261 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1044 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1043 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1072 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1073 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1047 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1041 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 1042 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 731 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 508 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 857 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 701 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 700 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 723 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 722 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 724 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 699 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 698 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 561 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 624 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 674 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 254 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 375 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 301 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 207 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 303 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 102 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 162 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 106 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 47 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 94 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 46 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 45 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 74 for   
[2025-07-22 15:56:50] local.INFO: DRY RUN: Would migrate appointment ID 25 for   
[2025-07-22 15:56:50] local.INFO: Clinic 5 completed: 38 processed, 0 skipped, 0 errors  
[2025-07-22 15:56:50] local.INFO: Starting appointments migration for clinic 4  
[2025-07-22 15:56:50] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:53] local.INFO: No appointments found for clinic 4  
[2025-07-22 15:56:53] local.INFO: Starting appointments migration for clinic 2  
[2025-07-22 15:56:53] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:56:58] local.INFO: No appointments found for clinic 2  
[2025-07-22 15:56:58] local.INFO: Starting appointments migration for clinic 1  
[2025-07-22 15:56:58] local.INFO: Making KiviCare API request: laravel_get_clinic_appointments  
[2025-07-22 15:57:00] local.INFO: Found 24 appointments for clinic 1  
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 313 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 161 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 137 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 160 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 159 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 151 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 155 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 156 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 157 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 133 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 135 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 54 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 56 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 80 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 79 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 82 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 65 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 43 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 20 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 55 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 60 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 58 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 57 for   
[2025-07-22 15:57:00] local.INFO: DRY RUN: Would migrate appointment ID 18 for   
[2025-07-22 15:57:00] local.INFO: Clinic 1 completed: 24 processed, 0 skipped, 0 errors  
[2025-07-22 15:57:00] local.INFO: Migration completed - Processed: 1002, Skipped: 0, Errors: 0  
[2025-07-22 15:57:00] local.INFO: Migration command completed successfully  
